"use client";

import React from 'react';
import type { FC } from 'react'
import Modal from './Modal';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { CheckIcon } from '@heroicons/react/24/outline'
import cn from 'classnames';
import { useTranslation } from 'react-i18next'
import ReactMarkdown from 'react-markdown'
import Link from 'next/link';
type IProps = {
  onClose?: React.MouseEventHandler,
  isOpen: boolean,
  onConfirm?: React.MouseEventHandler,
  comfirmBtn?: string,
  title: string,
  desc: string,
  isShowCancelBtn?: boolean,
  // 1 成功提示 -1 警告提示  2 : 没有icon
  status?: string
  isMd?: boolean
  isCloseIconShow?: boolean
  link?: string
}

const Alert: FC<IProps> = ({ onClose, isOpen, onConfirm, comfirmBtn, title, desc, isShowCancelBtn = true, status = '1', isMd = false, isCloseIconShow = true, link = ''}) => {
  const { t } = useTranslation()
  return (
    <Modal onClose={onClose} isOpen={isOpen} isCloseIconShow={isCloseIconShow}>
      <div className='min-h-20 mb-4'>
        <div className='flex p-1'>
        {status != '2' && <div className={cn("flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full  m-2 my-3 ", status == '1'? 'bg-green-500' : 'bg-red-100')}>
            {status == '1'? <CheckIcon className="h-6 w-6 text-white" aria-hidden="true" /> : <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />}
          </div>}
          <div className={cn('ml-3 mt-3 max-h-[calc(100vh-5rem)] overflow-y-scroll', !isMd && 'whitespace-pre-line')}>
            <h1 className='text-base font-semibold leading-6'>{title}</h1>
            <div className='text-sm mt-2'>{isMd ? <ReactMarkdown components={{
              a: ({node, ...props}) => <a target="_blank" className='text-blue-500 underline' rel="" {...props} />
            }}>{desc}</ReactMarkdown> : desc}</div>
          </div>
        </div>
      </div>
      <div className='px-2 py-2 flex flex-row-reverse'>
        {link? <Link href={link} target='_blank' onClick={onConfirm} className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>{comfirmBtn || t('app.common.comfirm')}</Link> : <button className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' onClick={onConfirm}>{comfirmBtn || t('app.common.comfirm')}</button>}
        {isShowCancelBtn && <button className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm ' onClick={onClose}>{t('app.common.cancel')}</button>}
      </div>
    </Modal>
  );
};

export default Alert;
