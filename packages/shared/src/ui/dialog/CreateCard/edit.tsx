"use client";
import React, { useContext, useEffect, useRef, useState } from 'react';
import type { FC } from 'react'
import {stopAudio} from '../../AudioPlay';
import { useForm, SubmitHandler } from "react-hook-form"
import Toast from '../../toast'
import useRequest from '../../../hook/useRequest';
import type { IProps, FormData } from './type';
import s from './style.module.scss'
import useCountToken from './useCountToken';
import CardName from './form/cardName';
import UserName from './form/user_name';
import NSFW from './form/nsfw';
import SimpleIntro from './form/simple_intro';
import Tags from './form/tags';
import SubTags from './form/subTags';
import Name from './form/name';
import Introduction from './form/introduction';
import Avatar from './form/avatar';
import Description from './form/description';
import FirstMessage from './form/first_message';
import <PERSON>Book from './form/roleBook';
import Speaker from './form/speaker';
import ExampleDialog from './form/example_dialog';
import Scenario from './form/scenario';
import ChatType from './form/chat_type'
import StatusBlock from './form/statusBlock'
import cn from 'classnames';
import { useTranslation } from 'react-i18next'
import AdaptModel from './form/adaptModel';
import Modal from '../Modal';
import ReplayLenRatio from './form/replay_len_ratio';
import MuilteScenes from './form/muilte_scenes';
import Lang from './form/lang';
import setDefault from './setDefault';
import useComfirm from '../../../hook/useComfirm';
import Author from './form/author'
import {validity} from './validity';
import InsertContent from '@share/src/module/InsertContent';
import autoReplace from './autoReplace';
import { Switch } from '@headlessui/react'
import Link from 'next/link';
import PlayType from './form/playType';
import useDialogAlert from '@share/src/hook/useDialogAlert';
import {customerBot} from '@/app/module/global'

const runsOnServerSide = typeof window === 'undefined'
// 暂存用户更新的数据
let storeFormData: any = {};
const CreateCard: FC<IProps> = ({ onConfirm, onCancel, isOpen, msg, userInfo }) => {
  const { t } = useTranslation()
  const [sumTokenLimit, setSumTokenLimit] = useState(10000)
  const [prevTokens, setPrevTokens] = useState<any>({});
  const [roleConfig, setRoleConfig] = useState<any>({})
  const request = useRequest()
  const cacheFormData = JSON.parse((!runsOnServerSide && localStorage.getItem('createCardCache')) || '{}');
  // 来源卡片编辑还是卡片上传
  const isReadMsg = msg?.isEdit || msg?.isFromImg;
  const [cropImg, setCropImg] = useState<Blob | null>(null)
  const [hasImg, setHasImg] = useState<boolean>(true)
  const [loading, setLoading] = useState(false)
  const [customLevelType, setCustomLevelType] = useState<any>(null)
  const levelType = customLevelType || roleConfig?.role?.level_type || msg?.level_type || 'normal';
  // 如果服务端是高级模式，不支持切换回普通模式
  const canSwitchMode = (roleConfig?.role?.level_type || msg?.level_type) === 'normal'? true : false;
  const confirm = useComfirm();
  const dialogAlert = useDialogAlert();
  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    control,
    setError,
    clearErrors,
    trigger,
    formState: { errors },
  } = useForm<FormData>({mode: 'onChange'})
  
  const onSubmit: SubmitHandler<FormData> = async (data) => {
    storeFormData = data;
    storeFormData.level_type = levelType;
    if(validity({storeFormData, tokens, Toast, t})) return;
    const formData = new FormData()
    if(msg?.isPublic) {
      setHasImg(cropImg != null || !!msg?.role_avatar)
      if(cropImg == null && !msg?.role_avatar) return;
      if(storeFormData.sub_tags && typeof storeFormData.sub_tags == 'string') {
        storeFormData.sub_tags = [storeFormData.sub_tags]
      }
    } else {
      // storeFormData.support_all_product = false;
      // 当storeFormData.support_product_ids是新手模式，没有字段，默认支持全部
      // if(!storeFormData.support_product_ids || storeFormData.support_product_ids.length === roleConfig?.chat_products.length) {
      //   storeFormData.support_all_product = true;
      // }
      storeFormData.personality = '';
    }
    msg?.id && (storeFormData.id = msg?.id);
    msg?.role_avatar && (storeFormData.role_avatar = msg.role_avatar)
    const roleBook = msg?.role_book || roleConfig?.role?.role_book || {};
    const roleBookEntries = storeFormData.role_book;
    if(roleBookEntries && roleBookEntries.length > 0) {
      // 转换为服务端需要的数组格式
      roleBookEntries.forEach((item: any) => {
        item.keys = item.keys === ''? [] : Array.isArray(item.keys)? item.keys : item.keys.split(/[,，]/)
        // 默认的数组，提交后会以数组形式
        // item.secondary_keys = item.secondary_keys === ''? [] : Array.isArray(item.secondary_keys)? item.secondary_keys : item.secondary_keys.split(/[,，]/)

        // 把probability保存到extensions字段
        item.extensions = {
          probability: (item.probability === undefined || item.probability === '')? 100 : Math.round(item.probability * 100) / 100
        }
      })
      storeFormData.role_book = {
        name: roleBook.name || storeFormData.name,
        book_id: roleBook.book_id,
        entries: roleBookEntries,
        enabled: true,
        extensions: roleBook.extensions || {}
      }
    } else {
      storeFormData.role_book = null;
    }
    // 由于前期错误引导，很多用户都用了单大括号，需要转换，将{user}{char}转换为{{user}}和{{char}}
    // if (storeFormData.status_block) {
    //   storeFormData.status_block = storeFormData.status_block.replace(/(?<!\{)\{(user|char)\}(?!\})/g, '{{$1}}');
    // }
    // if (storeFormData.status_block_init) {
    //   storeFormData.status_block_init = storeFormData.status_block_init.replace(/(?<!\{)\{(user|char)\}(?!\})/g, '{{$1}}');
    // }
    // if (storeFormData.status_block_rule) {
    //   storeFormData.status_block_rule = storeFormData.status_block_rule.replace(/(?<!\{)\{(user|char)\}(?!\})/g, '{{$1}}');
    // }
    formData.append('role_json', JSON.stringify(storeFormData));
    formData.append('avatar_img', cropImg || msg?.imgBlob || '')
    msg?.isPublic && formData.append('publish_card', 'true')
    const hideLoading = Toast.showLoading(t('app.dialog.creating'))
    try{
      const data = await request(msg?.isEdit? `/roles/update` : `/roles/create`, {
        method: 'POST',
        body: formData
      });
      hideLoading();
      if(data.error_code === 1002) {
        const res = await dialogAlert.show({
          title: t('app.cardEdit.public_reject_title'),
          desc: t('app.cardEdit.public_reject_desc'),
          comfirmBtn: t('app.cardEdit.public_reject_confirm'),
          alertStatus: '2'
        })
        if(res) {
          window.open(customerBot, '_blank')
        }
        return;
      }
      // console.log(data);
      
      if(msg?.isPublic) {
        Toast.notify({type: 'success', message: t('app.mine.public_success')})
      } else {
        Toast.notify({type: 'success', message: msg?.isEdit? t('app.mine.update_success') : t('app.dialog.create_success')})
      }
      // 新创建的卡片提交才清楚缓存
      !isReadMsg && localStorage.removeItem('createCardCache');
      onConfirm()
    } catch(e: any) {
      console.error('create or update Card error:', e);
      hideLoading();
      if(e.status == 403) {
        Toast.notify({type: 'error', message: t('app.cardEdit.auditing_desc')})
      } else {
        Toast.notify({type: 'error', message: t('app.dialog.create_fail')})
      }
    }
  }

  // 提交前做一次全局检查并弹窗提示
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit((data) => {
      onSubmit(data);
    }, async (errors) => {
      autoReplace({errors, setValue, formValues, confirm, t, Toast, trigger});
      Toast.notify({
        type: 'warning',
        message: t('app.cardEdit.form_error')
      });
    })(e);
  };

  stopAudio();
  const formValues = watch();
  const {tokens} = useCountToken(formValues, prevTokens, sumTokenLimit, userInfo)
  // 监听输入框的变化,计算token数量
  useEffect(() => {
    // 新创建的卡片才缓存
    !isReadMsg && localStorage.setItem('createCardCache', JSON.stringify(formValues))
  }, [formValues]);

  const getConfig = async () => {
    Toast.showLoading('Loading');
    try {
      const res = await request('/roles/create_config' + (msg?.id? `?role_id=${msg?.id}` : ''))
      setRoleConfig(res)
      res.role_token_count && setPrevTokens(res.role_token_count);
      msg?.role_token_count && setPrevTokens(msg?.role_token_count);
      res.sum_token_limit && setSumTokenLimit(res.sum_token_limit);
      setDefault(res, isReadMsg, msg, cacheFormData, reset, levelType);
      setLoading(true)
    } catch(e) {
      Toast.notify({
        type: 'error',
        message: t('app.common.load_err')
      })
      onCancel()
    } finally {
      Toast.hideLoading();
    }
  }
  useEffect(() => {
    getConfig();
  }, []);

  const switchMode = async () => {
    const newType = levelType == "normal"? 'premium' : 'normal';
    // if(newType === 'premium') {
    //   const res = await confirm.show('温馨提醒', '切换高级模式，提交成功后，将不能切回普通模式，是否继续？')
    //   if(!res) return;
    // }
    setCustomLevelType(newType)
  }
  // const formRef = useRef<any>(null);
  // // 表单提交失败处理函数
  // const onSubmitClick = async (e: any) => {
  //   // 让浏览器进行验证
  //   const isValid = formRef.current.reportValidity();
  //   handleSubmit(onSubmit)()
  // };

  return (
    <>
    <Modal onClose={() => {onCancel()}} isOpen={loading && isOpen} conWidth='lg:max-w-[950px] xl:max-w-[1100px]' isFull={true}>
      <div className='min-h-20'>
        <div className="flex min-h-full flex-1 flex-col justify-center">
        <div className="max-h-[calc(100vh_-_24px)]">
          <form className="" action="#" method="POST" onSubmit={handleFormSubmit}>
            <div className='max-h-[calc(88vh_-_24px)] overflow-auto space-y-6 px-1 sm:px-8 pt-4'>
            {msg?.isPublic? <>
              <Avatar errors={errors} register={register} s={s} msg={msg} hasImg={hasImg} setHasImg={setHasImg} setCropImg={setCropImg} />
              <Introduction errors={errors} register={register} s={s} msg={msg} tokens={tokens} formValues={formValues} />
              <CardName errors={errors} register={register} s={s}  />
              <SimpleIntro errors={errors} register={register} s={s} msg={msg} formValues={formValues} />
              {
                msg?.admin && <Tags errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
              }
              <MuilteScenes errors={errors} register={register} s={s} formValues={formValues} control={control} tokens={tokens} isPublic={msg?.isPublic} setError={setError} clearErrors={clearErrors} />
              <Author errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
              <SubTags errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
              <Lang levelType={levelType} errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} userInfo={userInfo} formValues={formValues} setValue={setValue}/>
              {/* <Personality errors={errors} register={register} s={s} msg={msg} tokens={tokens} /> */}
            </>
            : <>
              <Name errors={errors} register={register} s={s} msg={msg} formValues={formValues} trigger={trigger} setValue={setValue} />
              <UserName errors={errors} register={register} s={s} formValues={formValues} setValue={setValue} />
              <NSFW errors={errors} register={register} s={s}  />
              <PlayType errors={errors} register={register} s={s} msg={msg} roleConfig={roleConfig} />
              <ChatType register={register} msg={msg} levelType={levelType} />
              <Description levelType={levelType} errors={errors} s={s} register={register} formValues={formValues} tokens={tokens} setValue={setValue} />
              <ReplayLenRatio msg={msg} errors={errors} register={register} formValues={formValues} levelType={levelType} roleConfig={roleConfig} />
              <ExampleDialog errors={errors} register={register} s={s} formValues={formValues} control={control} tokens={tokens} />
              <MuilteScenes errors={errors} register={register} s={s} formValues={formValues} control={control} tokens={tokens} isPublic={msg?.isPublic} setError={setError} clearErrors={clearErrors} />
              <StatusBlock errors={errors} register={register} s={s} msg={msg} formValues={formValues} tokens={tokens} levelType={levelType} />
              {levelType != 'normal' && <div className="space-y-4">
                <h3>{t('app.cardEdit.role_book')}（{tokens.role_book.count} tokens）</h3>
                <RoleBook key="constant" errors={errors} levelType={levelType} register={register} s={s} control={control} msg={msg} tokens={tokens} formValues={formValues} trigger={trigger} setError={setError} clearErrors={clearErrors} constant={true} />
                <RoleBook key="non-constant" errors={errors} levelType={levelType} register={register} s={s} control={control} msg={msg} tokens={tokens} formValues={formValues} trigger={trigger} setError={setError} clearErrors={clearErrors} constant={false} />
              </div>}
              {/* <Scenario errors={errors} register={register} s={s} msg={msg} tokens={tokens} /> */}
              {/* <FirstMessage errors={errors} register={register} s={s} formValues={formValues} tokens={tokens} /> */}
              <AdaptModel levelType={levelType} errors={errors} register={register} msg={msg} roleConfig={roleConfig} formValues={formValues} />
              <Speaker roleConfig={roleConfig} errors={errors} register={register} s={s} tokens={tokens} watch={watch} msg={msg} />
              <Avatar errors={errors} register={register} s={s} msg={msg} hasImg={hasImg} setHasImg={setHasImg} setCropImg={setCropImg} />
              
            </>}
            <div className='text-base'>
                <h3 className='mb-1'>{t('app.cardEdit.tips_title')}</h3>
                <p className='text-sm pr-8 mb-2 text-gray-400 whitespace-pre-wrap'>
                  {t('app.cardEdit.tips')}
                  <br />
                  4. <Link className='text-blue-500 underline text-sm' href="/md" target='_blank' prefetch={false}>{t('app.cardEdit.md')}</Link>

                </p>
              </div>
            </div>
            
            <div className='relative max-h-[12vh] border-t border-gray-200 dark:border-gray-500'>
              {canSwitchMode && <label htmlFor="levelType" className='flex items-center dark:text-white text-gray-600 absolute py-2 px-1 text-sm top-2 border rounded-md dark:border-gray-500 dark:bg-gray-900 bg-white px-3'>
                {t('app.cardEdit.advanced_mode')}
                <Switch
                    id='levelType'
                    checked={levelType != "normal"}
                    onChange={() => { }}
                    onClick={switchMode}
                    className="group inline-flex h-[20px] w-8 items-center rounded-full bg-gray-300 dark:bg-gray-500 transition data-[checked]:bg-purple-500 dark:data-[checked]:bg-purple-500 ml-1"
                >
                    <span className="size-4 translate-x-[1px] rounded-full bg-white transition group-data-[checked]:translate-x-[14px]" />
                </Switch>
              </label>}

              <div className='px-2 pt-2 mb-0.5 flex flex-row-reverse'>
                <button key={1} className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' type="submit">{msg?.isPublic? t('app.cardEdit.public') : msg?.isEdit? t('app.common.update') : t('app.common.submit')}
                  {msg?.public_role_id === 0 && userInfo?.publish_task_switch && msg?.isPublic && <div className='text-xs'>{t('app.approval_for_gift')}</div>}
                </button>
                <button className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm' type='button' onClick={() => {onCancel()}}>{t('app.common.cancel')}</button>
              </div>
              <p className={cn('text-right text-sm pr-2', tokens.sum_book.count / tokens.sum_book.max > 1 && 'text-red-500')}>{t('app.dialog.sum_cost')}: {tokens.sum_book.count}/{tokens.sum_book.max}tokens</p>
            </div>
          </form>
        </div>
      </div>
      </div>
      <InsertContent setValue={setValue}  />
      </Modal>
    </>
  );
};

export default CreateCard;