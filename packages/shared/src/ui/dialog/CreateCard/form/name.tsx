import { useTranslation } from 'react-i18next'
import { getByteLength } from '../../../../module/stringTool';
import { useEffect } from 'react';
import Link from 'next/link';

const Name = ({ errors, register, s, msg, formValues, trigger, setValue }: any) => {
    const { t } = useTranslation()
    const nameVal = formValues['name'];
    useEffect(() => {
        // 角色变化，主动触发校验
        if (nameVal) {
            trigger('user_role_name');
        }
    }, [nameVal])
    return <>
        <div className='sm:w-full sm:max-w-sm'>
            <div className='flex items-center'>
                <label htmlFor="name" className="block text-sm font-medium leading-6 ">
                    {t('app.dialog.role_name')} {errors.name ? (
                        <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.name.message}</span>) : <span className='text-xs mt-1'>*</span>
                    }
                </label>
                <Link className='ml-2 text-sm text-blue-500 underline' href="https://telegra.ph/幻夢冩卡教程-04-15" target="_blank">{t('app.common.guide')}</Link>
            </div>
            <div className="mt-2">
                <input
                    id="name"
                    {...register("name", {
                        onBlur: (e: any) => {
                            const value = e.target.value;
                            // 进行trim处理
                            const filteredValue = value.trim();
                            if (filteredValue !== value) {
                                setValue('name', filteredValue);
                            }
                        },
                        required: t('app.dialog.require'),
                        validate: {
                            len: (value: any) => {
                                const byteLength = getByteLength(value);
                                // console.log('byteLength', byteLength, value);
                                return byteLength <= 30 || t('app.dialog.name_len');
                            },
                            // // 不能和user_role_name重复
                            // repeat: (value: any) => {
                            //     const userRoleName = formValues['user_role_name'];
                            //     return !userRoleName || value !== userRoleName || t('app.cardEdit.name_repeat');
                            // }
                        }
                    })}
                    type="text"
                    autoComplete="name"
                    required
                    placeholder={t('app.dialog.role_placholder')}
                    className={`${s.inputStyle} ipt`}
                />
            </div>
        </div>
    </>
}

export default Name