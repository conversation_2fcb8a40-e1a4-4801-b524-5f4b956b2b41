"use client";

import React from 'react';
import type { FC, ReactNode } from 'react'
import Modal from './Modal';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { useTranslation } from 'react-i18next'
import cn from 'classnames';
type IProps = {
  onClose: React.MouseEventHandler,
  isOpen: boolean,
  onConfirm: React.MouseEventHandler,
  onCancel: React.MouseEventHandler,
  children: React.ReactNode,
  comfirmBtn?: string,
  showCancelBtn?: boolean,
  icon?: ReactNode,
  cancelBtn?: string
  isShowIgnore?: boolean,
  ignoreCb?: any
  showIcon?: boolean
  isCloseIconShow?: boolean
  cancelBtnStyle?: string
}

const Confirm: FC<IProps> = ({ onClose, isOpen, children, onConfirm, comfirmBtn, showCancelBtn = true, icon, cancelBtn, isShowIgnore = false, ignoreCb, showIcon = true, isCloseIconShow = true, cancelBtnStyle = '', onCancel }) => {
  const { t } = useTranslation()
  return (
    <Modal onClose={onClose} isOpen={isOpen} isCloseIconShow={isCloseIconShow}>
      <div className='min-h-20 mb-4'>
        <div className='flex p-1'>
          {showIcon && <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-red-100 m-2 my-3">
            {icon? icon : <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />}
          </div>}
          <div className='ml-3 mt-3'>
            {children}
          </div>
        </div>
      </div>
      <div className='px-2 py-2 flex flex-row-reverse'>
        <button className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' onClick={onConfirm}>{comfirmBtn || t('app.common.comfirm')}</button>
        {showCancelBtn && <button className={cn('p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm', cancelBtnStyle)} onClick={onCancel}>{cancelBtn || t('app.common.cancel')}</button>}
        {isShowIgnore && <label className='mr-2 flex items-center text-xs text-gray-400'><input name='showTips' onChange={(e) => {ignoreCb(e.target.checked)}} className='mr-0.5' type="checkbox" />{t('app.dialog.no_show_tip')}</label>}
      </div>
    </Modal>
  );
};

export default Confirm;
