'use client'

import { createContext, useState, ReactNode, useEffect, useLayoutEffect } from 'react';
import Toast from './ui/toast'
import { getCookie, setCookie, deleteCookie } from 'cookies-next';
// import useRequest from './hook/useRequest';
import { setCommonHeader, commonHeader } from "./module/commonHeader";
import BalanceConfirm from './ui/dialog/balance-confirm';
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation';
import * as Sentry from "@sentry/nextjs";
import {hash} from '@share/src/module/urlTool'
import useDialogLogin from './hook/useDialogLogin';
import sdk from '@share/src/module/sdk'
import {isTG} from '@share/src/module/global'
import useDialogAlert from './hook/useDialogAlert';
const base64url = require('base64url');
import { tgWeb } from '@/app/module/global';

export enum LoginStatus {
  pending = -1,
  success = 1,
  failed = 0
}
interface AuthContextValue {
  user: User | null;
  login: (email: string, password: string, idToken?: string) => Promise<void>;
  logout: () => void;
  logoutSilent: () => void;
  isLogin: boolean,
  updateUserInfo: (userInfo: any) => void,
  showBalanceConfirm: () => void,
  fetchUser: () => void,
  isAndroidTg: boolean,
  isTG: boolean,
  telegramLogin: (telegramData: any) => Promise<void>,
  tgCodeLogin: (telegramData: any) => Promise<void>,
  loginStatus: number
}

export interface User {
  id?: string;
  nickname: string;
  email?: string;
  avatar?: string
  from?: 'TG' | 'OTHER',
  model?: string[],
  regex_rules?: any[],
  roles?: any[]
  recent_roles?: any[]
  speaker_list?: any[]
  chat_products?: any[]
  user_model?: string,
  enable_nsfw?: boolean,
  // 美国站
  show_nsfw?: boolean,
  payed_user?: boolean,
  // 是否模糊敏感图片
  show_nsfw_image?: boolean,
  // 限制创建角色数量
  max_role_count?: number
  // 聊天详情页是否展示生成建议对话提示
  show_chat_tips?: boolean
  // 聊天背景图片
  image_bgs?: string[]
  // 选择的背景图片序号
  selected_bg_index?: number
  // 是否使用个人通用聊天背景
  use_personal_bg?: boolean
  // 背景透明度
  opacity?: number
  // 官方背景图片
  official_bgs?: string[]
  // 显示日榜/周榜/月榜标签
  display_summary_rank_tag?: string
  // PAID,FREE_BENEFIT，代表付费通道，免费权益通道
  chat_channel?: string
  // 状态栏开关
  status_block_switch?: boolean
  // 是否展示任务icon
  publish_task_switch?: boolean
  // 首页默认暂时哪个tag
  initial_active_tag?: boolean
}
const { notify } = Toast

export const AuthContext = createContext<AuthContextValue | null>(null);

export const AuthProvider = ({ children, lang }: { children: ReactNode, lang: string }) => {
  const { t } = useTranslation()
  const token = getCookie('token')
  let storeUser: any = {
    show_nsfw_image: true
  };
  const router = useRouter()
  const dialogLogin = useDialogLogin();
  const [user, setUser] = useState<User | null>(storeUser);
  const [loginStatus, setLoginStatus] = useState<number>(LoginStatus.pending)
  const dialogAlert = useDialogAlert()

  // console.log('loginStatus', loginStatus, token);
  const isLogin = loginStatus === LoginStatus.success
  setCommonHeader({'current-language': lang});
  const updateUser = (res: any) => {
    setUser(n => {
      return {
        ...n,
        ...res
      }
    });
  }

  // 账号密码登录
  const login = async (email: string, password: string, idToken?: string) => {
      let url;
      let data;
      // google等授权登录
      if(idToken) {
        url = `${process.env.NEXT_PUBLIC_API_HOST}/auth/token`;
        data = JSON.stringify({
          id_token: idToken
        })
      // 普通登录
      } else {
        url = `${process.env.NEXT_PUBLIC_API_HOST}/user/login`;
        data = JSON.stringify({
          email: email,
          password: password,
        })
      }
      const response = await fetch(url, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: data
      })
      const res = await response.json();
      if (response.ok) {
          notify({ type: 'success', message: t('app.login.login_sucess') })
          console.log(res);
          fetchUser();
      } else {
          throw new Error(res?.msg || res?.detail[0]?.msg);
      }
  };

  const logout = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/user/logout`, {credentials: 'include'})
      if (response.ok) {
        logoutSilent()
        // location.reload();
        location.href = '/'
      } else {
        throw new Error('退出登录异常')
      }
    } catch (e) {
      console.log('退出登录异常');
    }
  };
  const logoutSilent = () => {
    setUser(null);
    setLoginStatus(LoginStatus.failed)
    deleteCookie('token')
  }

  const updateUserInfo = (userInfo: any) => {
    updateUser({...user, ...userInfo})
  }
  const fetchUser = async () => {
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/user/me`, {
        credentials: 'include',
        headers: commonHeader
      })
      if(res.ok) {
        const data = await res.json();
        updateUser({...user, ...data})
        Sentry.setUser({ email: data.email, id: data.id, username: data.nickname });
        // 一个月有效期 todo:用服务的过期时间
        setCookie('token', '1', {
          maxAge: 2592000, // 30 天
          path: '/'
        });
        setLoginStatus(LoginStatus.success)
      } else if (res.status == 401) {
        console.log('no login or token expired');
        logoutSilent()
        dialogLogin.show({
          login: login,
          telegramLogin: telegramLogin
        });
      } else {
        const err = new Error('login err code: ' + res.status);
        console.error('login err code: ' + res.status, res.statusText);
        Sentry.captureException(err);
        Toast.notify({
          type: 'error',
          message: t('app.login.login_failed') + ` ${res.status}`
        })
      }
    } catch(e) {
      Sentry.captureException(e);
      dialogAlert.show({
        isMd: true,
        alertStatus: -1,
        title: t('app.common.net_err'),
        desc: t('app.common.net_err_desc')
      })
    }
  }
  const [isAndroidTg, setIsAndroidTg] = useState(false)
  const telegramLogin = async (telegramData: any) => {
    Toast.showLoading('');
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/tg_auth_login`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...commonHeader
        },
        body: JSON.stringify({"auth_data": JSON.stringify(telegramData)})
      });
      const res = await response.json();
      if (response.ok) {
        notify({ type: 'success', message: t('app.login.login_sucess') })
        console.log(res);
        fetchUser();
      } else {
          throw new Error(res?.msg || res?.detail?.[0]?.msg);
      }
    } catch (error) {
      console.error('Telegram login error:', error);
      setLoginStatus(LoginStatus.failed);
      throw error;
    } finally {
      Toast.hideLoading();
    }
  };
  const tgCodeLogin = async ({uid, session_code}: any) => {
    Toast.showLoading('');
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/tg_code_login`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...commonHeader
        },
        body: JSON.stringify({
          uid: uid,
          session_code: session_code
        })
      });
      const res = await response.json();
      if (response.ok && res.code === 0) {
        notify({ type: 'success', message: t('app.login.login_sucess') })
        console.log(res);
        fetchUser();
      } else {
          throw new Error(response.status + ' ' + res?.message || 'tgCodeLogin login error');
      }
    } catch (error) {
      console.error('Telegram login error:', error);
      setLoginStatus(LoginStatus.failed);
      throw error;
    } finally {
      Toast.hideLoading();
      router.push('/');
    }
  };
  useLayoutEffect(() => {
    console.log('useLayoutEffect');
    // console.log('tgWebApp', tgWebApp);
    console.log('window.Telegram?.WebApp?.initDataUnsafe', sdk.platform);
    updateUser({...user, ...{
      from: isTG? 'TG' : 'OTHER'
    }})
    if (isTG) {
      if(sdk.platform == 'android') {
        setIsAndroidTg(true)
      }
      sdk.expand();
      sdk.enableClosingConfirmation();
      sdk.disableVerticalSwipes();
      // sdk.setBackgroundColor("#000000")
      // sdk.requestFullscreen();
      let initTGData = sdk.initData || localStorage.getItem('tg-init-data') || '';
      const searchParams = new URLSearchParams(location.search);
      const tgData = new URLSearchParams(decodeURIComponent(initTGData));
      // isReload 为1时，不做后面的自动跳转逻辑
      const isReload = hash.get('reload')
      // console.log('isReload', isReload);
      // 如果切换多语言重新刷新页面，需要从locastorage中读取
      const commanHeader: any = {'tg-init-data': initTGData}
      const botId = searchParams.get("botid") || localStorage.getItem('botid')
      if(botId) {
        commanHeader['tg-bot-id'] = String(botId)
        localStorage.setItem('botid', String(botId))
      }
      // tg 英文版小程序,botid=abc_1-eng
      if(tgWeb) {
        const _botId = botId?.split('-')[0]
        const eng_bot = botId?.split('-')[1]
        commanHeader['tg-bot-id'] = String(_botId)
        commanHeader['eng-bot'] = String(eng_bot)
      }
      localStorage.setItem('tg-init-data', String(initTGData))
      setCommonHeader(commanHeader);
      fetchUser();
      const startParam = tgData.get('start_param');
      // 跳转到充值页面弹窗
      // const startParam = 'u_27-sd_9';
      // 挑到群聊卡聊天页面
      if(startParam && isReload != '1') {
        // u_27-r_-e_history/myroles u_x 渠道来源 r_x 角色id e_x 额外base64的数据
        // e_x协议 {p: xx} p: 跳转路径
        // g_x协议 群组卡片
        // https://base64.guru/standards/base64url/encode 通过此工具，把{}数据传入
        try{
          const parts = startParam.split('-');
          const result: any = {};
          parts.forEach(part => {
              const [key, value] = part.split('_');
              result[key] = value;
          });
          // 角色卡聊天页面
          const roleId = result.r;
          // 群聊卡聊天页面
          const groupId = result.g;
          // 角色详情
          const roleDetailId = result.rd;
          // 分享聊天页面
          const shareId = result.sd;
          // 用户的对外页面
          const userId = result.uid
          // 额外的数据，可以跳转到其他特殊路径
          const extra = result.e;
          
          if(roleId) {
            router.push(`/${lang}/chat?roleid=${roleId}&type=deeplink`)
          } else if(groupId) {
            router.push(`/${lang}/chat?groupid=${groupId}`)
          } else if(roleDetailId) {
            router.push(`/${lang}/role?type=single&id=${roleDetailId}`)
          } else if(shareId) {
            router.push(`/${lang}/chat/share/${shareId}`)
          } else if(userId) {
            router.push(`/${lang}/user/${userId}/share`)
          } else if(extra) {
            const extraObj = JSON.parse(base64url.decode(extra))
            router.push(`/${lang}/${extraObj.p}`)
          }
        } catch(e) {
          console.log('startParam err')
          Sentry.captureException(e);
        }
      }
    } else {
      if(!!token) {
        fetchUser();
      } else {
        // 如果是tg url code登录，不弹出登录框
        const searchParams = new URLSearchParams(window.location.search);
        const uid = searchParams.get('uid');
        const session_code = searchParams.get('session_code');
        if (uid && session_code) {
          tgCodeLogin({
            uid,
            session_code
          })
        } else {
          setLoginStatus(LoginStatus.failed)
          dialogLogin.show({
            login: login,
            telegramLogin: telegramLogin
          });
        }
      }
    }
  }, []);

  const [isBalanceConfirmOpen, setIsBalanceConfirmOpen] = useState(false)
  const showBalanceConfirm = () => {
    setIsBalanceConfirmOpen(true)
  }

  // function updateVH() {
  //   let vh = window.innerHeight * 0.01;
  //   document.documentElement.style.setProperty('--vh', `${vh}px`);
  // }
  // useEffect(() => {
  //   updateVH();
  //   window.addEventListener('resize', updateVH);
  //   return () => {
  //     window.removeEventListener('resize', updateVH);
  //   };
  // }, []);

  return (
    <>
    <AuthContext.Provider value={{ user, login, logout, isLogin, logoutSilent, updateUserInfo, showBalanceConfirm, isAndroidTg, fetchUser, isTG, loginStatus, telegramLogin, tgCodeLogin }}>
      {children}
    </AuthContext.Provider>
    <BalanceConfirm isOpen={isBalanceConfirmOpen} onClose={() => {setIsBalanceConfirmOpen(false)}} />
    </>
  );
};