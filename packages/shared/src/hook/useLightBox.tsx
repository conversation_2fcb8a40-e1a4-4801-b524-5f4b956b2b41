import React, { useState } from 'react'
import { createRoot } from 'react-dom/client'
import { XMarkIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'
import { RotateCw } from 'lucide-react';

const useLightBox = () => {
  return {
    show: ({ src, unoptimized = false }: any) => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      
      const close = () => {
        root.unmount()
        document.body.removeChild(holder)
      }

      return new Promise((resolve, reject) => {
        const LightBox = () => {
          const [isLoading, setIsLoading] = useState(true)

          return (
            <div
              onClick={(e) => {
                e.stopPropagation()
                close()
              }}
              className='fixed z-50 w-full h-full left-0 top-0 bg-black/[.75]'
            >
              <div className='absolute w-[min(450px,98vw)] h-min m-auto left-0 right-0 top-0 bottom-0'>
                {isLoading && (
                  <div className='absolute flex justify-center items-center w-full h-full'>
                    <RotateCw className="animate-spin text-blue-500" size={32} color="#a855f7" />
                  </div>
                )}
                <Image
                  className={`w-[100%] h-auto max-h-[90vh] object-contain ${isLoading ? 'opacity-100' : ''}`}
                  src={src || '/dot.png'}
                  alt='img'
                  width={400}
                  height={600}
                  loading='eager'
                  quality={90}
                  unoptimized={unoptimized}
                  onClick={(e) => {
                    e.stopPropagation()
                  }}
                  onLoad={() => setIsLoading(false)}
                />
                <div className='mt-6 text-center'>
                  <button className='h-10 w-10' onClick={(e) => {e.stopPropagation(); close()}}>
                    <XMarkIcon className='h-10 w-10 text-white'></XMarkIcon>
                  </button>
                </div>
              </div>
            </div>
          )
        }

        root.render(<LightBox />)
      })
    },
  }
}

export default useLightBox
