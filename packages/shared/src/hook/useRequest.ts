import { useCallback } from "react";
import { AuthContext } from "../authContext";
import { useContext } from "react";
import { commonHeader } from '../module/commonHeader';
import useDialogLogin from './useDialogLogin'
import * as Sentry from "@sentry/nextjs";
import useDialogRegist from '@share/src/hook/useDialogRegist';
// import useDialogAlert from "./useDialogAlert";
// import { useTranslation } from "react-i18next";
import Toast from "../ui/toast";

export enum RequestStatus {
  OK = 0
}
const useRequest = () => {
    // const {open} = useDialog();
    const auth = useContext(AuthContext);
    const dialogLogin = useDialogLogin();
    const dialogRegist = useDialogRegist();
    // const dialogAlert = useDialogAlert();
    // const { t } = useTranslation();
    return useCallback((url: string, option?: RequestInit, host?: boolean) => {
        return new Promise<any>(async (resolve, reject) => {
            if(!option) {
              option = { headers: commonHeader }
            } else if (option.headers) {
              Object.assign(option.headers, commonHeader)
            } else {
              option.headers = commonHeader
            }
            try {
              const response = await fetch(`${host? url : process.env.NEXT_PUBLIC_API_HOST + url}`, {
                credentials: 'include',
                ...option
              });
              if(response.ok) {
                const res = await response.json();
                resolve(res);
              } else if (response.status == 401) {
                const searchParam = new URLSearchParams(location.search);
                const code = searchParam.get('code');
                // 来自邀请的并且没登录，弹注册框
                if(code) {
                  dialogRegist.show(code)
                } else {
                  dialogLogin.show();
                  auth?.logoutSilent();
                }
                if(auth?.isTG) {
                  console.log('授权问题', code);
                  const err = new Error('ohter err code: 401');
                  throw err
                }
                // Sentry.captureException(err);
              } else if (response.status == 402) {
                auth?.showBalanceConfirm()
                // const err = new Error('ohter err code: 402');
                // console.error('ohter err code: 402');
                // Sentry.captureException(err);
                reject(response)
              } else if (response.status == 403) {
                const res = await response.json();
                Toast.notify({
                  type: 'error',
                  message: res.message || (res? String(res) : '403'),
                })
                reject(response)
                const err = new Error('ohter err code: ' + response.status, res?.error_code);
                throw err
              } else {
                const err = new Error('ohter err code: ' + response.status);
                reject(response)
                throw err
              }
            } catch (error: any) {
                console.error(`Failed to fetch ${url}`, error);
                reject(error)
                if (error.name !== 'AbortError') {
                  // Sentry.captureException(error);
                  throw error
                }
            }
        })
    }, []);
}

export default useRequest;