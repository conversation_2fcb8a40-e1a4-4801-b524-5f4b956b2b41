'use client'
import classNames from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import Alert from '../ui/dialog/alert';

const useDialogAlert = () => {
  return {
    // 1 成功提示 -1 警告提示  2 : 没有icon
    show: ({title, desc, isShowCancelBtn = false, alertStatus = '1', comfirmBtn, isMd = false, isCloseIconShow=true, link = ''}: any) => {
      let holder = document.createElement('div')
      holder.className = 'absolute z-50 w-full h-full left-0 top-0'
      document.body.appendChild(holder)
      const root = createRoot(holder)
      return new Promise((resolve, reject) => {
        const onComfirm = () => {
          resolve(true)
          root.unmount();
          document.body.removeChild(holder);
        }
        const onCancel = () => {
          root.unmount();
          document.body.removeChild(holder);
          resolve(false);
        }
        root.render(<Alert isOpen={true} onConfirm={onComfirm} onClose={onCancel} title={title} desc={desc} isShowCancelBtn={isShowCancelBtn} status={alertStatus} comfirmBtn={comfirmBtn} isMd={isMd} isCloseIconShow={isCloseIconShow} link={link} />);
      })
    },
    hide: () => {
      // if (holder) root.unmount();
    }
  }
}

export default useDialogAlert
