import privacy from './privacy.en'
import terms from './terms.en'
import chat from './module/chat.en'
import share from './module/share.en'
import img from './module/img.en'

const translation = {
  img,
  approval_for_gift: 'Approved for 🟡',
  gift: 'Award',
  chat,
  share,
  meta: {
    title: 'Dream AI Companion',
    desc: 'Dream AI Companion, AI chat, SillyTavern platform',
    copyright: '',
    privacy_policy: ''
  },
  common: {
    free_benefit_end: 'Free benefit does not need to be queued for validity period: ',
    gen_img: 'Image',
    execute_success: 'Snapshot generated successfully! ',
    status_setting_title: 'Status bar setting instructions',
    status_setting_desc: `1. Click the button to hide or display the status bar of all character cards
    2. Hide means that the AI ​​will no longer display the status bar when replying
    3. Display means that the status bar will be displayed under the AI ​​reply text

    Instructions:
    1. Some character cards do not have a status bar and are not affected by the status bar display and hide settings;
    2. You can also set the status bar display and hide settings in the mini program-my-settings`,
    status_setting: 'Status bar settings',
    save_success: 'Save successfully',
    update_rule: 'Update rule',
    tpl: 'Template',
    generate_status_bar: 'One-click to generate status bar',
    select_template: 'Select template',
    status_bar: 'Status bar',
    ai_generate_tips: 'This function is generated by AI intelligence. AI will combine the development of your current chat history to generate the status bar of the entire chat. It costs 600💎 to generate it once',
    ai_generate: 'Loading...',
    support_all_lang: 'Support all languages',
    upload: 'Upload',
    more: 'more',
    less: 'close',
    go_setting: 'Setting',
    public_test: 'In beta',
    net_err: 'Network abnormality',
    net_err_desc: `Please try the following methods and try again:
1. Switch VPN nodes
2. Change VPN to global mode
If the above two methods fail to solve the problem, please contact [customer service](https://t.me/uhoney_help01_bot)
`,
    claim:'claim',
    confirmBtn1: 'Go',
    cancelBtn1: 'Cancel',
    tips1: 'Reminder: 💎Insufficient balance',
    act_join_success: 'Registration successful',
    act_join_failed: 'Registration failed',
    confirm1: 'Got it',
    confirm2: 'Registration',
    message: 'Message',
    back: 'Back',
    model: 'Model',
    comfirm: 'Confirm',
    logout: 'Logout',
    regist: 'Register',
    login: 'Login',
    del: 'Delete',
    cancel: 'Cancel',
    update: 'Update',
    submit: 'Submit',
    loading: 'Loading...',
    load_err: 'Network Error, please try again',
    retry: 'Retry',
    del_seccess: 'Deleted successfully',
    exec_err: 'Execution exception',
    tips: 'Tips',
    save: 'Save',
    back_home: 'Back',
    pay: 'Recharge',
    save_back: 'Save',
    minutes_ago: 'minutes ago',
    hours_ago: 'hours ago',
    days_ago: 'days ago',
    count_msg: 'Message',
    role_info: 'Role Information',
    role_introduction: 'Role Introduction',
    guide: 'Writing card tutorial',
  },
  login: {
    tg_login: 'Login with Telegram',
    login_sucess: 'Login successful',
    login_failed: 'Login failed, please try again',
    login_title: 'Login',
    email: 'Email',
    password: 'Password',
    other_login: 'Or login with the following account',
    no_account: 'No account?',
    login_now: 'Register now',
    regist_title: 'Register',
    password_confirm: 'Confirm Password',
    verifyCode: 'Verification Code',
    invite_code: 'Invitation Code (optional)',
    regist: 'Regist',
    password_len: 'Password must be at least 6 characters',
    password_not_match: 'Two passwords do not match',
    regist_success: 'Registration successful, please log in~',
    regist_err: 'Sorry, there was a problem with the registration, please try again later~',
    sended: 'Sent',
    sended_desc: 'The verification code has been sent to the registered email address, please log in to your email to check',
    get_code: 'Get the verification code'
  },
  setting: {
    theme: 'Theme',
    enable_nsfw: 'Display Mature Content',
    enable_nsfw_desc: 'By enabling Mature Content, you confirm you are over the age of 18.',
    setting: 'Setting',
    system: 'System',
    dark: 'Dark',
    light: 'Light'
  },
  toast: {
    wait_data: 'Waiting for data to return, please try again later~',
    group_not_support: 'Direct chat with a bot is currently not supported for group cards',
  },
  nav: {
    img: 'Image',
    checkin_gift: 'Check in and receive',
    index: 'Cards',
    chat: 'Chat',
    mine: 'Mine',
    pay: 'Pay',
    create: 'Create',
    claim_gift: 'Claim',
    title: 'Dream AI Companion1',
    char: 'Character List',
    tag: 'Tag Management',
    presets: 'Presets',
    reg: 'Regex',
    operation: 'Operation',
    setting: 'Settings',
    createCard: 'Create Card'
  },
  footer: {
    about: 'About',
    contact: 'Contact Us',
    terms: 'Terms',
    privacy: 'Privacy Policy',
  },
  index: {
    task: 'Welfare Task',
    nsfw: 'NSFW',
    more: 'more',
    hide: 'hide',
    loading: 'Loading',
    no_card_load: 'No more roles to load.',
    search_place_holder: 'Search by name, keywords, or tags',
    nsfw_alert_title: 'Need to log in',
    nsfw_alert_desc: 'To activate the filtering function, you need to log in to the system and enable the nsfw in the Settings',
    regist_title: 'Register',
    author: 'Author',
    filter: {
      tag: 'Tags',
      chat_mode: 'Mode',
      independent_role: 'Role',
      speech_filter: 'Speech',
      sort: 'Sort',
      default: 'All',
      all_model: 'All Modes',
      select_tag: 'Select Tag',
      clear: 'Clear',
      mother_son: 'mother and son',
      contrast: 'contrast',
      ntr: 'NTR',
      licentious: 'licentious',
      virgin: 'virgin',
      incest: 'incest',
      fanfiction: 'fanfiction',
      gender: 'Gender'
    }
  },
  search: {
    search_res: 'Search Result',
    no_res: 'No Results',
    create_role: 'Create Your Character',
    noMsg: 'Message cannot be empty'
  },
  mine: {
    snapshoot1: 'Snapshot',
    card: 'Card Record',
    snapshoot: 'My Snapshot',
    extra_info_name_same: 'The player name in the supplementary profile has been automatically replaced with {{user}}',
    voice_title: 'Automatically generate voice description',
    voice_desc: `1. If turned on, the text content in ""()[] will be automatically generated into voice (recommended, faster generation)
2. If turned off, all text will be generated into voice (it takes time to wait for generation)`,
    voice: 'Voice settings',
    auto_generate_voice: 'Automatically generate voice',
    switch_identity_tip: 'Switch identity tip',
    switch_identity_desc: `1. After switching identities, all user names and avatars in the context dialogue will be modified
2. After switching identities, the additional content set by the user will take effect`,
    switch_identity: 'Switch Identity',
    cannot_delete_enabled: 'Role selection status does not support deletion',
    switch_success: 'Switch successful',
    select_user: 'Select an identity',
    vip_limit: 'Limited time experience',
    user_role: 'Player Interaction Identity',
    select: 'Select',
    extra_info_desc: `1. When chatting with AI, supplementing the role settings of the player interaction identity you play will affect the dialogue or plot advancement
2. The role of the player interaction identity in the role card has been set, and there may be a conflict and incomplete effectiveness
3. The maximum number of tokens for the supplementary settings of the player interaction identity is 150`,
    extra_info_placeholder: `Enter additional information for player settings, token limit 150
For example: {{user}} height 180cm, IQ 200
Special note: The player name here cannot be the same as the nickname, only {{user}} can be used`,
    extra_info_title_placeholder: `Limited to 15 characters`,
    extra_info_limit: 'Generate up to 3 player supplementary information, and the upper limit of the content token that is effective at the same time is 150 (supports multiple selections)',
    extra_user_limit: 'Generate up to 3 user identities',
    play_type: 'Game Type',
    next_receive_at: 'Next receive time',
    bg_transparent: 'Background Transparency',
    upload_limit_bg: 'You can only upload 2 chat backgrounds at most',
    chat_bg: 'Chat background',
    switch_language: 'Switch language',
    use_common_bg: 'Use personal common chat background',
    benifit_consume: 'Benefits Consumed',
    benefits: 'Benefits',
    benefits_recharge: 'Benefits Recharge', 
    benefits_give: 'Benefits Give',
    benefits_free: 'Free benefits',
    no_benefit: 'No benefits',
    quota: 'Chat Quota',
    validity_period: 'Validity Period',
    expire_amount: 'Expired diamond quantity',
    expire_time: 'Expiration time',
    no_record: 'No record',
    history_tips: 'Tip: Only the consumption records for the last three months starting from January 9, 2025 and later are displayed.',
    expire_tips: 'Tip: Only show expiration records in the last three months starting from January 9, 25',
    date_format: 'YYYY/MM/DD',
    no_more_list: 'Loaded to the bottom ',
    type: 'Type',
    card_name: 'Role card name',
    card_id: 'Role card ID',
    chat_mode: 'Chat mode',
    consume_diamond: 'Consume',
    diamond_consume_time: 'Consumption time',
    diamond_expire: 'Expiration Record',
    consume_record: 'Consumption record',
    diamond_record: 'Diamond record',
    show_chat_tips: 'Chat light bulb reminder switch',
    setting: 'Settings',
    nsfw_alert_desc: 'You need to turn on the nsfw switch on the homepage to turn off the image blur function',
    blur_img: 'Blur Image',
    upload_failed: 'Upload failed~',
    exceed_limit: 'Exceeds the limit',
    nopay_card_limit: 'Non-paying users can only create up to 6 character cards, while paying users can create up to 15 cards',
    pay_card_limit: `Non-paying users can only create up to 6 character cards, and paying users can create up to 15 cards`,
    pay_card_limit1: `Non-paying users can only create up to 6 character cards, and paying users can create up to 15 cards

[Certified Author] can create more cards❗️`,
    only_three_group_card: 'Non-VIP users can only create up to 3 group chat cards, VIP function will be launched soon, please look forward to it',
    find_customer_service: 'Certified Author',
    vip_title: 'VIP function',
    vip_desc: 'Available after VIP activation, VIP function will be launched soon, please look forward to it',
    my_role: 'My Created Characters',
    create: 'Create',
    my_group: 'My Group Chat Card',
    upload_tip: 'You have not created character cards yet, please click to create or upload',
    upload_group_tip: 'You haven’t created a group chat yet. Click “Create Group Chat” to create one.',
    fav_role_tip: 'There is no favorite character, you can add it to your favorites on the character details page',
    no_content: 'No content yet',
    recent_chat: 'Recent Chats',
    recent_chat_tip: 'No characters chatted recently',
    update_success: 'Update successful~',
    public_success: 'Submitted, under review~',
    create_failed: 'Creation failed, please try again~',
    person_info: 'Personal Information',
    user_id: 'ID',
    nick_name: 'Nickname',
    input_your_name: 'Please enter your character name',
    avatar: 'Avatar',
    update: 'Update',
    beta: 'Beta',
    upload_card: `Upload Role Card`,
    upload_card_title: 'Import character description',
    upload_card_desc: `This platform is compatible with character cards from other platforms and supports the most common PNG format in the [Character Card V1/V2 Specification](https://github.com/malfoyslastname/character-card-spec-v2/blob/main/spec_v2.md) standard cards. Please export the characters of Agnai, Tavern, SillyTavern, Risu, Chub, PygmalionAI, and JanitorAI from these platforms and upload them here.`,
    my_share: 'My share',
    user_share: 'User Share',
    user_public: 'User Release',
    fav: 'My Favorites'
  },
  pay: {
    wechat_btn: `「WeChat」alternative channel`,
    alipay_btn: `「Alipay」alternative channel`,
    recharge_explain1: 'After the recharge is completed, the money will be credited to your account within 5 minutes. <1>Contact customer service</1>',
    channel_bk: 'Alternative channel',
    pay_wechat_channel_btn1: `WeChat Pay`,
    pay_wechat_channel_btn2: `WeChat Pay`,
    pay_alipay_channel_btn1: `Alipay Pay`,
    pay_alipay_channel_btn2: `Alipay Pay`,
    pay_channel_desc: 'If you cannot complete the recharge, you can choose [Card Code Recharge] 100% success',
    pay_methods: 'Payment Methods',
    diamond_intro: '1 diamond = 1 gold coin',
    exchanging: 'Exchanging',
    sorry_login_desc: 'Sorry, there was a problem logging in, please try again later~',
    card_active: 'Card Activation',
    input_code: 'Enter the activation code',
    active: 'Activate',
    charge_success: 'Recharge successful',
    charge_success_desc: `Recharge successful, diamonds have been issued to the game~ If not received, please contact customer service`,
    charge_failed: 'Recharge failed',
    charge_failed_desc: 'Sorry, there was a problem with the recharge, please try again~',
    charge_err: 'Recharge exception, reason:',
    remain_diamond: 'Remaining Diamonds',
    code_exchange: 'Card Code Exchange',
    inventory: 'Product List',
    recharge_history: 'Recharge History',
    amount: 'Amount (USDT)',
    diamond: 'Diamonds',
    balance: 'Balance',
    explain: 'Explanation',
    create_time: 'Creation Time',
    expire_time: 'Expiration Time',
    recharge_explain: 'Charging explanation',
    diamond_intro_desc: `1. 1💎=1🟡
2. In principle, 🟡 is consumed first and then 💎 is consumed. However, if 💎 is about to expire, this part of 💎 should be consumed first, and then 🟡 is consumed.

Example:
(a) If 💎🟡 are both valid for a permanent period, 🟡 is consumed first, and then 💎 is consumed after 🟡 consumption reaches 0.
(b) If 💎🟡 are both valid for a period, 🟡 is consumed first; if 💎 is about to expire (expires before 🟡), the 💎 that is about to expire is consumed first until it reaches 0, and then 🟡 is consumed.
`,
    overtime: 'Payment Timeout',
    pay_failed: 'Payment Failed',
    copyed_success: 'Copied to clipboard',
    paying: 'Paying',
    usdt_pay_desc: 'You can transfer from any wallet or exchange to the above address',
    time_remain: 'Remaining Payment Time',
    order_id: 'Order ID',
    network: 'Network',
    charge_amount: 'Recharge Diamonds',
    tips: 'Warm Tips:',
    tips1: '<0>Do not</0> deposit USDT-ERC20 or TRC20 to the above address, or the funds <2>will be unrecoverable</2>',
    order_created: 'Order Created',
    order_created_desc: `The payment link has been pushed to the Bot page. Please click the payment button below to jump to the Bot page and click the payment link to pay.
    (If you cannot jump after clicking the button, please close this miniapp and find the push link in the Bot to continue the operation)`,
    pay: 'Pay Now',
    order_created_desc1: 'Please click the payment button below to jump to the payment page to pay. If you have already paid, please close this window',
    pay1: 'Pay Now',
    charging: 'Recharging',
    charging_desc: 'Please click "Recharge Completed" after successful recharge',
    charging_desc1: `1. Select the recharge method of 💎direct transfer. After the recharge is completed, 💎 will be transferred to the account within 5 minutes.
2. If the payment is blocked, please click "alternative channel" to pay.
(Tip: There is also "{{payChannel}}" in the card code purchase)`,
    finish_charge: 'Recharge Completed',
    charge_err1: 'Recharge exception, please try again',
    btn1: 'Having trouble recharging? Click here',
    method: 'Solution',
    method_desc: 'Payment may fail due to browser compatibility issues. You can copy the link below and try paying in another browser (recommended: Chrome, QQ Browser, UC Browser, Quark Browser, etc.)',
    charge_success_desc1: 'Diamonds have arrived~',
    pay_success: 'Payment Successful',
    no_money: 'Insufficient Balance',
    no_money_desc: 'Sorry, your USDT balance is insufficient, please recharge and try again',
    charge: 'Recharge Now',
    charge1: 'Alipay Recharge 1',
    charge2: 'WeChat Recharge',
    usdt_charge: 'USDT Recharge',
    credit_card: 'Credit Card Recharge',
    start_pay: 'Star Recharge',
    ali2: 'Alipay Recharge 2',
    bank: 'UnionPay',
    wechat_alipay: 'Wechat/Alipay',
    wechat_alipay_subtitle: '(recommended)',
    wechat: 'Wechat scan code payment',
    wechat_subtitle: '(💎direct to account)',
    alipay: 'Alipay scan code payment',
    alipay_subtitle: '(💎direct to account)',
    wechat1: 'Wechat scan code payment1',
    Unionpay: 'Unionpay scan code payment',
    copy_fail: 'Copy failed!',
    usdt_warn: `Deposit amount must <1>match the above amount</1>, otherwise it may not be processed in time.
    <br/> For exchange wallets, ensure that <5>the post-fee amount</5> matches the above.`,
    min: 'min',
    sec: 'sec',
    copy_success: 'Copied to clipboard, open another browser and paste the URL to make the payment',
    copy_addr: 'Copy payment address',
    purchase_tips: '(No card code? Click <1>here</1> to purchase)',
    exchange_guide_title: 'Activation Code Usage Instructions',
    exchange_guide_desc: `
1. Click the button ‘WeChat/Alipay Recharge.’ After purchasing on the redirected page, enter your phone number or QQ number to retrieve the code.

2. Copy the code

3. On this page, click "Redeem Code" -- Enter the code

4. [Important] If you cannot find the card code after purchase, please contact customer service @xlfkwkf_bot (long press to copy)`,
    view_btn: 'View Illustrated Guide',
    exchange_btn: 'Card Code Instructions',
    recharge_btn: 'Recharge Instructions',
    recharge_guide_title: 'Recharge Instructions',
    recharge_guide_desc: `
WeChat/Alipay Recharge:
Recharge using a card activation code. After successful recharge, select "Card Redemption" in the remaining diamonds section, enter the activation code, and the diamonds will be credited.

WeChat QR Code Payment:
Recharge directly via WeChat QR code. After successful payment, the diamonds will be credited.

Alipay QR Code Payment:
Recharge directly via Alipay QR code. After successful payment, the diamonds will be credited.

USDT Recharge:
Recharge via exchange/wallet (we use the Polygon chain, which has very low fees, and does not support other chains). After successful payment, the diamonds will be credited.`,
    recharge_guide_desc_tgweb: `
    USDT Recharge:
    Recharge via exchange/wallet (we use the Polygon chain, which has very low fees, and does not support other chains). After successful payment, the diamonds will be credited.`,
    guideClose: 'Close and Go to pay',
    permanent_validity: 'Permanent'
  },
  operation: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    clear: 'Clear',
    save: 'Save',
    edit: 'Edit',
    refresh: 'Restart',
    send: 'Send',
    copy: 'Copy',
    lineBreak: 'Line Break',
  },
  dialog: {
    status_block_placeholder: 'After selecting the status bar template, you can still edit the status bar (total token limit is 500)',
    edit_status_block: 'Custom status bar description',
    edit_status_block_desc: `**Creation method:**
    1. You can select a status bar template and create a status bar based on the template; at the same time, after selecting the status bar template, you can use AI to generate the status bar with one click
    2. You can customize the status bar, edit the status bar and update rules by yourself

    **Charging instructions:**
    1. 600💎 will be deducted when selecting a status bar template or customizing the status bar
    2. When selecting a status bar template, 600💎 has been deducted when AI generates the status bar with one click (the content generated by AI has not been changed). At this time, no 600💎 will be deducted when saving; if the content is changed after AI generates the status bar with one click, 600💎 will be deducted when saving.
    3. AI consumes 600💎 to generate the status bar with one click (600💎 will be consumed each time you click AI to generate the status bar with one click)

    **Custom status bar AI review instructions:**
    1. AI review failed, cannot be saved and used
    2. AI review passed, can be saved and used

    **Status bar hiding and display instructions:**
    1. Click the button to hide or display the status bar of all role cards (My-Settings)
    2. Hidden, means AI replies will no longer display the status bar
    3. Displayed, means AI replies will display the status bar`,
    cant_submit: 'Unable to submit! ',
    cant_submit_desc: `Currently {{char}}, {{user}} have format errors, do you want to switch to the correct format?

Click yes to automatically change to the correct format for you
Click no to change manually`,
    yes: 'Yes',
    no: 'No',
    update_success: 'Update successful',
    delComfirm: 'Do you want to delete?',
    play: 'Play',
    right_title: 'This card comes from the internet',
    right_title1: 'This card is created by the author',
    right_desc: 'If there is infringement, please feedback to the official for deletion',
    right_desc1: 'If there is any infringement, please report it to the official website, and the official website will contact the author to assist in handling it.',
    avatar: 'Character Avatar',
    avatar_err: 'No image uploaded',
    cardName: 'Card Name',
    require: 'Required',
    len_18: 'Cannot exceed 18 characters',
    name_place_holder: 'Used for card title display',
    chat_model: 'Chat',
    role_model: 'Role Playing',
    desc_placeholder: 'Include AI roles, named user roles, relationships, personality traits, appearance, preferences, abilities, age, gender, race, nationality, and ethnicity. This setting will not be shown to others.',
    desc_title: 'Character Definition',
    example_title: 'Dialogue Example',
    desc_err_token: 'Exceeded maximum tokens',
    first_title: 'First Message',
    first_desc: 'Opening line, the first sentence the character greets',
    name_len: 'Length exceeds limit (30 characters)',
    role_name: 'AI Character Name',
    role_placholder: 'Name of the character AI is playing',
    intro_name: 'Card Introduction',
    intro_placeholder: 'Detailed description of character features, bond conflicts, special gameplay, etc. This is only for display and does not affect the performance of AI.',
    personality_title: 'Personality',
    personality_placeholder: 'Set the character’s personality',
    is_public: 'Public?',
    scenario_title: 'Initial Scenario',
    simplt_intro: 'Marketing Copy',
    limit_desc: '(Within {{n}} characters)',
    limit: 'Cannot exceed {{n}} characters',
    simplt_intro_placeholder: 'Displayed in the character list, brief introduction of bonds, conflicts, character charms, etc. For display only, does not affect AI role-playing',
    voice: 'Voice',
    status_block: 'Status Bar',
    status_block_select: 'Please select status bar type',
    normal: 'Standard',
    collapse: 'Collapse',
    hide: 'Hide',
    status_tmp: 'Status Bar Template',
    status_sample: `Example:
Mood: {mood}
Desire value: {desire value}
Location: {location}
Vagina status: {Vagina status}
Relationship with {{user}}: {relationship with {{user}}
Appearance and clothing: {appearance and clothing}`,
    status_init_desc: `Example:
Mood: excited, anxious
Desire value: 33
Location: public place
Vagina status: a little excited and wet
Relationship with {{user}}: a classmate who goes to school together, I have some feelings for him
Appearance and clothing: clean and tidy, hair ends are a little messy`,
    status_rule_desc: `Example:
The sexual desire value range is 0-100, and the initial value is 33
Sexual desire value update rules:
When {{char}}'s sexual desire is aroused, it rises, and when it is suppressed, it falls. The sexual desire value in a single conversation does not exceed 10
When {{char}} reaches climax, the sexual desire value drops by 20
Clothing content should be detailed enough, including underwear, socks and accessories. If there are exposed parts, they should be described in detail`,
    status_init: 'Initial Status Bar Content',
    status_rule: 'Status Bar Update Rules',
    label: 'Label',
    max_tags_err: 'The number of tags cannot exceed 8',
    label_err: 'At least one non-NSFW label needs to be selected',
    cat: 'Category',
    create_success: 'Creation successful~',
    creating: 'Creating..',
    create_fail: 'Creation failed, please try again~',
    sum_token_exceed: 'Total tokens exceeded limit',
    next: 'Next',
    prev: 'Prev',
    sum_cost: 'Total Cost',
    operate_success: 'Operation successful~',
    model_select: 'Model Selection',
    review_reject: 'Rejection Reason',
    no_show_tip: 'Don’t show this again'
  },
  cardEdit: {
    public_reject_title: 'Review violation',
    public_reject_desc: `You have violated the review regulations for publicly releasing role cards and have been restricted from submitting for review.

[Apply for review resumption] Please contact customer service❗️`,
    public_reject_confirm: 'Contact customer service',
    sys_tool: 'System and Tools',
    play_type_title: 'Game Type Description',
    play_type_desc: `Opponent play: AI does not interrupt, AI plays one or more roles
Plot push: AI will write all the plots, players make key decisions in it, and AI will supplement
System tools: various AI assistants, generators, simulators, including novel generation, character generation, story generation, game simulation, specific world simulation, etc.`,
    opponent_content: 'Opponent',
    push_content: 'Push the plot',
    init_heat: 'Initial popularity',
    final_heat: 'Current popularity',
    deduct_heat_title: 'How popularity is deducted',
    deduct_heat: 'Deducted popularity',
    deduct_heat_desc: `1. After being disliked by the user, the heat value will be deducted;
2. When the AI ​​judges the character card, the heat value will be deducted when the selected mode cannot advance the plot;

Improvement method:
1. When reviewing the character card, it is not recommended to check the extreme speed mode and the following modes for cards with complex logic`,
    adapt_model_at_least_one: 'Choose at least one applicable model',
    adapt_model_desc1: 'Choose carefully: AI will influence the popularity of character cards according to the actual situation of the plot according to the selected mode. If the selected mode cannot advance the plot, the popularity of character cards will be reduced.',
    adapt_model_title: 'Chat Mode Description',
    adapt_model_desc: `Speedy Mode: Super fast, but cannot handle complex logic, and will get stuck when pushing the plot
    Charm Mode: Full-blooded version of the Internet celebrity model DeepSeek plus deep training, fast speed, online IQ, can drive some complex logic
    Desire Mode: Coquettish personality, enhanced desire, easier to push down, can handle complex logic
    World Card Mode: High IQ model and best cracking tuning, supports complex card driving, explosive writing, clear logic
    Ultra-long Memory Mode: Best mode for group chat, high-end model with 3 times super long memory, high IQ, good writing, suitable for driving complex cards and pushing super long plots
    All-round Mode: Combines the advantages of all modes, super high IQ, super memory, super explosive writing, strong ability to simulate complex role-playing and grand world cards, often unexpectedly makes you feel that she is a real person`,
    md: 'Markdown Preview',
    create1: 'Manual creation',
    tips_title: 'Instructions',
    advanced_mode: 'Advanced mode',
    role_noconstant_content_placeholder: 'The maximum number of tokens for input content is 1000',
    primy_key_placeholder: 'Multiple keywords are separated by commas, with a maximum of 20 characters',
    len_20: 'The content length cannot exceed 40 characters',
    role_content_placeholder: 'There is no token limit for input content, but it is counted in the total consumption of 6000 tokens',
    constant_role_book: 'Continuous effective world book',
    key_role_book: 'Keyword world book',
    comment_placeholder: 'Limited to a maximum of 30 characters',
    collaspe_err: 'You need to fix the form error before you can collapse',
    form_error: 'Please check if the form is filled out correctly',
    ai_name_repeat: 'Cannot be the same as the AI role name',
    no_user_name: 'You need to use {{user}} to replace the user role name: {{userName}}',
    no_char_name: 'You need to use {{char}} to replace the AI ​​role name: {{charName}}',
    no_user_name1: 'You cannot include the user role name: {{userName}}',
    token_limit_exceeded: 'Cannot exceed {{limit}} tokens',
    adapModel: 'Applicable Mode',
    AIName_placeholder: 'Name of the character AI is playing',
    user_role_name: 'User Character Name',
    user_role_name_placeholder: `It won't show, but helps the AI`,
    ai_formate: 'Reply Mode',
    example_placeholder: 'The dialogue example has a strong guiding effect on the AI reply. The AI will imitate the language style, content structure, character, preferences, etc. of this example. It needs to conform to the card mode, please use direct text and parentheses () for Chat Model',
    step1: 'Basic Settings',
    step2: 'Public Card Settings',
    public: 'Publish',
    example_dialog_user_placeholder: 'Enter user dialogue here',
    example_dialog_ai_placeholder: 'Enter AI role dialogue here',
    example_dialog_user_placeholder_chat: `Auntie Zhang, you go to the park by yourself`,
    example_dialog_ai_placeholder_chat: `Xiao Ming, how about we go together? (Example, light chat "non-dialogue" such as scene action psychology, etc. are put in (), "language text or dialogue" is directly output without modification`,
    replay_len: 'AI Reply Length',
    replay_len_title: 'Description',
    replay_len_desc: `Dialogue examples are extremely instructive for AI responses. AI will imitate the language style, content structure, personality, preferences and other personalities reflected in the language.

Need to comply with card mode:

1. In Chat Model, "non-dialogue" such as scenes, actions, psychology, etc. are placed in (), and "language, text or dialogue" are output directly without modification;

2. For role play, put "language, text or dialogue" in "";

Both of these modes force the AI ​​to bind a fixed role, such as: Black Widow, Pirates of the Caribbean NPC collection (the collection represents a character), a system, and a novelist.`,
    muilte_scenes_title: 'Dialogue Scenes',
    muilte_scenes_illustrate_title: 'Chat Model instructions',
    muilte_scenes_illustrate: 'Chat Model, "non-dialogue" such as scene, action, psychology, etc. are placed in (), "language, text or dialogue" is directly output without modification',
    muilte_scenes_init: 'Initial Scene',
    muilte_scenes_init_desc: 'I happened to meet Tom in the park (example, Tom is the user name)',
    muilte_scenes_first_message: 'First message',
    muilte_scenes_first_message_desc: `(I am very happy to see Tom) Tom, what are you doing here? (For example, Chat Model "non-dialogue" such as scene action psychology is put in (), "language text or dialogue" is directly output without modification`,
    muilte_scenes_first_message_role_desc: `I was very happy to see Tom, "Tom, what are you doing here?" (Example, role play, put "language, text or dialogue" in "")`,
    lang_title: 'Set Supported Languages',
    auditing_desc: 'Under Review, you need to pass the review before you can publish again',
    edit: 'Edit',
    editing: 'Editing',
    auditing: 'Auditing',
    approve: 'Approve',
    reject: 'Reject',
    select_model: 'Select the creation method',
    new_model: 'Beginner Mode',
    advanced_model: 'Advanced Mode',
    enable_beginer_tpl: 'Enable novice template',
    user: 'User',
    char: 'Char',
    add_sample: 'Add examples',
    add_dialog_context: 'Add conversation scene',
    normal_model: 'General',
    gender: 'Gender',
    gender_desc: 'Female (example)',
    age: 'Age',
    age_desc: '18 (example, minimum age 18)',
    personality: 'Personality',
    personality_desc: 'Lustful, easily jealous (example, can be multiple)',
    appearance: 'Appearance',
    appearance_desc: 'Height 165cm, sweet appearance, high school JK uniform (example) appearance (body shape, facial features, clothing)',
    sexual: 'Sexual preference',
    sexual_desc: 'Odor fetish, sensitive to rejection (example, can be multiple)',
    add: 'Add',
    role_book: 'Lorebooks',
    order: 'Order: ',
    enable: 'Enable: ',
    primy_key: 'Primary Keywords: ',
    sec_key: 'Optional Filter: ',
    trigger: 'Trigger(0-100): ',
    content: 'Content: ',
    status: 'Status: ',
    constant: 'Constant',
    normal: 'Normal',
    status_title: 'Status Bar Description',
    status_desc: `1. Tags are not supported; please do not enter any tags.
        2. The system automatically adds support for \`\`\` code blocks; no need to input them separately.
        
        Example:
        #character name}
👚Clothing Status: {character's detailed clothing at the moment}
💭Inner Thoughts: {character's inner thoughts at the moment}
`,
    reply_title: 'Reply Mode Description',
    reply_desc: `1. Chat Model: For "non-dialogue" elements such as scene actions or thoughts, place them in parentheses (). For "language text or dialogue," output directly without modifications.
2. Role Play: Place "language text or dialogue" in quotation marks "".
3. General Model: no perspective agreement, no text format agreement, no dialogue output requirements, and you need to agree on it yourself.

1 and 2 are both forced to bind AI to a fixed role, such as: Xiao Zhang, Black Widow, Pirates of the Caribbean (NPC collection, multiple people together represent a role), a system, a novelist.

3 is suitable for self-defined roles, systems, simulators, novelists, narrators, cameras, monitors, etc.
In both of these modes, the AI is forced to assume a fixed role, such as: Black Widow, a group of Caribbean Pirate NPCs (where the group represents one role), a system, or a novelist.`,
    role_book_title: 'World Book Description',
    role_book_desc: `! ! Advanced function, use with caution! !
Keywords can trigger special scene gameplay, and at the same time can strengthen AI's memory of keywords. World Book has caught up with other major Internet platforms in AI role card function

Suggestion: Users who do not understand World Book should use with caution`,
role_book_constant_title: 'Continuous World Book',
role_book_constant_desc: `1. Unlimited number of entries
2. For each continuous world book, the entry content input has no limit on the token length, but it is counted in the total consumption of 6000 tokens`,
role_book_key_title: 'Keyword World Book',
role_book_key_desc: `1. The upper limit of the token input for each entry content is 1000
2. Unlimited number of entries, but the upper limit of the content token is 1000 each time it is triggered
3. Set multiple keywords (comma separated) to trigger the same entry. When the latest two rounds of conversations in the chat (two messages from users, two messages from AI) match one of the keywords, the entry is triggered
4. When the same keyword triggers multiple items at the same time in the latest two rounds of conversations (two messages from users and two messages from AI), or multiple keywords trigger multiple items, the upper limit of the trigger content token is 1000
·The higher the order, the higher the triggering priority
·When the order is the same, if the upper limit of the content token of multiple items is less than 1000, all items will be triggered; if the upper limit of the content token of multiple items exceeds 1000, the excess will be truncated`,
  author: `Author's name`,
  showAuthor: 'Show Author',
  author_desc: `If you not choose Show Author, the author name displayed on the role card is: Anonymous
If you choose Show Author, the author name displayed on the role card is: Your current user name`,
    data: 'Data',
    cardName: 'Card Name',
    uv: 'UC',
    chatRound: 'Popularity',
    likeCount: 'Likes',
    auditng: 'Under review',
    audit_desc: 'The card is under review and cannot be operated until the review is completed',
    low_img: 'Upload failed',
    low_img_desc: 'The width cannot be less than 400 pixels and the height cannot be less than 600 pixels',
    tips: `1. When the review is passed, the card content (picture\status bar\character settings, etc.) may be modified
2. Private cards are only visible to you
3. If the review is rejected 6 times in a row, it will be restricted from being published publicly`,
    del_failed1: 'Cards under review cannot be deleted',
    has_private_card_desc: `There is a private card in the group chat that cannot be published
Suggestions:
1. The private card is published publicly first
2. For the card that is successfully published publicly, you can choose to join the group chat through "Create Group Chat-Search". All role cards are public cards and can be published in the group chat. `,
    no_user_or_Char: 'Cannot contain {{user}}',
    max_9: 'The maximum number of world book entries is 9.',
    max_constant_entries: 'Continuous World Book cannot exceed 3 entries at most',
    max_non_constant_entries: 'No more than 6 non-constants',
    quick_type: 'Quick Input'
  },
  history: {
    no_archive: 'No chat history',
    group_card: 'Group chat card',
    vip_feature: 'Thirst trap',
    archive: 'Load Game',
    load_archive: 'Load',
    del_history: 'Delete History',
    first_chat_at: 'First chat',
    latest_chat_at: 'Last chat',
    tip: 'Please select at least 2 roles~',
    my_role: 'My Role',
    my_group: 'My Group Chat',
    group_title: 'Group Chat Creation & Deduction Rules',
    group_desc: `	1.	You can choose 2-4 AI character cards, either from the Dream Platform or ones you create yourself.
	2.	Tips for a better group chat experience:
	•	Same story background and mutual understanding: characters from well-known stories (e.g., Journey to the West, One Piece), or characters sharing the same story background, or character cards that introduce relationships with others.
	•	Same language style, reply format, and similar status display.
	3.	In a group chat, users manually click an AI character avatar to trigger a reply, or click a dice icon for a random AI character to respond.
	4.	Deduction rules: In group chats, users choose a chat mode, and diamonds are deducted each time an AI character initiates a reply (e.g., 100 diamonds deducted in extreme speed mode when an AI character is clicked).`,
    create_group: 'Create Group Chat',
    group_scenario: 'The beginning of the storyline',
    group_scenario_desc: 'Clearly explain the relationship between the characters, the scene, the upcoming events, etc., to facilitate the subsequent interaction between the characters',
    rec_card: 'Recommended Role Cards',
    my_card: 'My Role Cards',
    select_role: 'Select Group Chat Role',
    select_role_desc: 'Only cards with {{char}} as independent roles can be selected',
    search_placeholder: 'Search Role Cards',
    tip1: 'The group chat cannot exceed 4 roles',
    no_create_card: 'No cards created yet, please go to "Chat" - "My Cards" to create one',
    search_empty: 'No search results found',
    name: 'Group Chat Name',
    name_placeholder: 'Show in the character list',
    story_bg: 'Group chat introduction',
    story_bg_desc: 'Detailed introduction to character features, bond conflicts, special gameplay, etc., for display only and does not affect AI role-playing',
    scenario: 'Initial Scenario',
    scenario_desc: 'Enter the initial scenario description, the scene where the story begins, displayed on the group chat details page'
  },
  errorMessage: {
  },
  lang: {
    zh: '简体中文',
    en: 'English',
    'zh-TW': '繁体中文'
  },
  lan_short: {
    zh: '简体',
    en: 'En',
    'zh-TW': '繁体'
  },
  privacy,
  terms,
  recentChat: {
    del_chat: 'Del Chats',
    export_chat: 'Export',
    export_card: 'Export card'
  },
  sort: {
    approved: 'approved',
    reject: 'review rejected',
    no_publish: 'not published',
    default: 'Default'
  }
}

export default translation
