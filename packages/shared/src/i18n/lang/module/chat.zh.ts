
const chat = {
    queue_end: '排队结束，请重试～',
    queue_desc: '免费模式排队，前方排队{{remainPerson}}人，预计排队{{remainTime}}秒',
    queue_dialog_title: '⚠️免费模式排队提醒',
    queue_dialog_desc: `当前免费聊天模式使用量大，需排队等待❗️❗️
前方排队{{remainPerson}}人，预计排队{{remainTime}}秒

说明：聊天消耗免费聊天模式次数，使用的算力是共享的，稳定性差、模型慢

建议：
1. 切换【其他聊天模式】继续聊天
2. 充值9.9元，免费模式聊天不排队，持续7天`,
    queue_dialog_comfirm_btn: '9.9包周不排队',
    queue_dialog_cancel_btn: '关闭并等待',
    snapshot_generate_ing: '快照生成中',
    go_view: '去查看',
    backup_card_title: '什么是快照？',
    backup_card_comfirm: '生成快照',
    backup_card_cancel: '忍住不用',
    backup_card_desc: `a.我们将您和AI聊天记录归纳总结，作为新的初始场景第一句话、角色重新定义，强化设定后生成新的角色卡；
b.快照会加强AI记忆；
c.快照生成大约需要1分钟；

快照使用说明：
a.TG小程序聊天页面右上角增加「快照」按钮，点击「快照」后随时生成1张新的“快照角色卡”的私有卡，保存在我的-卡片记录-我的快照，供以后随时聊；
b.每次从「我的-卡片记录-我的快照」点击，都是开始新的回合继续聊，会加入聊天列表存档；
c.「快照」功能，使用单次扣除1500💎`,
    backup_card_success: '查看路径：我的-卡片记录-我的快照',
    model_not_exist: '当前选择的模型已下线，已切换到推荐模型:{{name}}',
    same_model: '当前模式和目标模式相同',
    vip_model_desc:'尊享[快速]：聊天消耗💎金币，会优先保障模型的稳定性及高效的文字输出',
    share_model_desc:'共享[慢速]：聊天消耗免费聊天模式次数，使用的算力是共享的，稳定性差、模型慢、文字输出需要排队',
    new_user_recharge_tips: '恭喜🎉获得新用户充值大礼包',
    new_user_recharge_tips_desc: '充多送多，仅首充一次机会哦！聪明的朋友已经选择赠送比例多的套餐啦',
    switch_model_tip: '切换模式：更高级的模式AI会更聪明、更高情商、更真人',
    benefit_title: '一份见面礼',
    benefit_desc: `🎉恭喜你！被幸运大礼包砸中！平台免费赠送你60次聊天权益！（完成任务可每天领取奖励）`,
    benefit_btn: '立刻点击领取',
    benefit_claim_status: '领取成功',
    tip_change_mode_auto_free: '自动切换到模型：{{to_model_name}}',
    tip_change_mode_auto1: '当前模式：{{from_model_name}}免费额度不足，已切换到：{{to_model_name}}',
    free_benifit: '共享',
    paid_benifit: '尊享',
    no_free_benefit1: '该聊天模式，剩余次数不足，请切换其他模式',
    remain: '剩余',
    net_slow: '慢速',
    net_fast: '快速',
    net_off: '无',
    switch_share: '共享',
    switch_pay: '尊享',
    tab_share: '【共享AI算力】',
    tab_pay: '【尊享AI算力】',
    current: '当前',
    switch_consume_comfirm: `尊享
（使用💎金币）`,
    switch_consume_cancel: `共享
（使用免费次数）`,
    switch_consume: '选择扣费方式',
    switch_consume_desc: `请选择和AI聊天的扣费方式
1. 共享，聊天消耗奖励的免费聊天模式次数
2. 尊享，聊天消耗💎金币`,
    switch_failed1: '请等AI回复完成再进行切换操作',
    tip_change_mode_man: '手动切换到：{{to_model_name}}',
    tip_change_mode_auto: '此卡不支持{{from_model_name}}，已经自动切换到：{{to_model_name}}',
    tip_change_mode_entry: '当前模式：{{to_model_name}}',
    no_banlance_msg: '余额不足，充值后继续畅聊',
    go_charge: '去充值',
    dice_tooltip: "点击骰子，随机一个AI回复",
    avatar_tooltip: "点哪个AI头像，哪个AI回复",
    tokens: 'token数',
    switch_top_free_model: '切换{{model_name}}',
    btn_pay: '最低9.9体验高级模式',
    switch_model: '选择{{model}}',
    continue_model: '继续使用{{model}}',
    switch_desc: `❗️ 测试不通过的模式：{{not_pass_model}}

上述这些模式，AI聊天体验会受到影响哦，例如角色设定不生效、回复出现乱码、剧情推进困难等

✅ 建议的聊天模式：{{supported_model}}
上述模式经过作者测试，AI可以正常发挥。
    
注意：更高级的模式AI会更聪明、更高情商、更真人、文笔剧情更精彩哦`,
    switch_desc1: `此卡不支持{{model_name}}，已经自动切换到{{target_model_name}}模式`,
    switch_desc2: `❗️ 测试不通过的模式：{{not_pass_model}}
    
上述这些模式，AI聊天体验会受到影响哦，例如角色设定不生效、回复出现乱码、剧情推进困难等
    
✅ 建议的聊天模式：{{supported_model}}
上述模式经过作者测试，AI可以正常发挥。
    
注意：更高级的模式AI会更聪明、更高情商、更真人、文笔剧情更精彩哦`,
    repeat_like: '你已经点过赞了',
    repeat_dislike: '你已经点过踩了',
    like_success: '点赞成功',
    dislike_success: '踩成功',
    copy_success: '复制成功',
    copy_fail: '复制失败',
    msg_too_long: '内容上限{{limit}}token，已超出限制请调整后发送',
    net_err1: '消息异常',
    net_err_desc: `该消息由于网络等原因，没有发送成功。下次进入该对话或者刷新页面，系统会自动清理无效消息`,
    no_share: '还没有用户分享过聊天内容',
    square: '精彩广场',
    private: '个人创建私有卡',
    card_intro: '卡片介绍',
    newChat: '新对话',
    init_scenario: '初始场景：',
    empty: '未填写',
    private_card: '私有卡',
    content_err: '内容异常，状态码: {{status}}, 说明: {{errMsg}}，请重试～',
    net_err: '网络异常，本次对话不扣费，请重试～',
    recall: '撤回',
    copy: '复制',
    retry: '重试',
    tips: '温馨提示：',
    comfirm: '知道了',
    tips_desc: `AI可以帮助您生成对话，继续推动剧情，但每点击一次小灯泡，会扣除当前聊天模式所需消耗的💎
    
你确认，额外消耗💎通过AI帮你生成对话吗？`,
    wating_desc: '等待数据返回中，请稍后再试～',
    warn: '提醒',
    warn_desc: `开始新聊天，支持选择最近的3次聊天记录，可在以下位置读取存档：
位置一：小程序-聊天-角色卡信息
位置二：小程序-角色卡聊天窗口右上角设置中
是否继续`,
    try_ai_answer: '试一试 AI回答',
    cancel_reply: '取消回复',
    person_card: '个人上传卡片',
    person_limit: '仅限个人使用',
    price: '钻石/条',
    switch_success: '切换成功',
    switch_failed: '切换失败',
    msg_not_empty: '消息不能为空',
    show_all_chats: '默认展示{{n}}轮对话',
    view_all: '查看全部记录请<1>点击</1>',
    export_all: '导出历史记录请<1>点击</1>',
    history: '聊天历史',
    export_title: '已導出',
    export_desc: '聊天记录已通过bot推送，请回到bot页面下载',
    del_failed: '执行异常，请重试',
    img: '发私照',
    img_title: '【限时特惠】性感私照，专属给你！',
    img_title1: '【限时特惠】性感私照，不满意？！',
    img_desc: '想要更亲密的互动吗？原价2000💎，特惠期间仅需500💎，你的专属AI角色将为你发送一张性感私房照，绝对私人订制，独享亲密时刻！千万别错过这份为你准备的独家惊喜！',
    img_desc1: '可以重新生成哦，重试价格400💎，原价2000💎，2折！2折！2折！原有生成的私照都会保留，再生成一张新的性感私房照！',
    img_confirm_btn: '查看私照',
    img_cancel_btn: '忍住不看',
    one_img_tips: '同一个回复只能生成一张图片哦～',
    load_fail: '图片加载失败',
    retry_tips_title: '温馨提示',
    retry_tips_desc: `嗨，想要让Ta的回应更贴合你的心意吗？😍 每次重试都会让AI重新生成这句话，并扣除一次聊天费用哦。如果你之前点击了“发私照”，重试后照片也会消失，重新发私照需要单独消耗钻石～💎

不过别担心，Ta会为你送上更加让你满意的回复！✨ 感受更完美的互动，只需再轻轻一点～你要重试这次聊天吗？
`,
    retry_tips_cancel_btn: '再想想，先不重试',
    switch: '立即切换',
    keep: '保持',
    start_chat: '开始私密聊天',
    fav: '收藏',
    faved: '已收藏',
    fav_success: '收藏成功，可在 我的-我的收藏 查看',
    del_fav_success: '已取消收藏，可在 我的-我的收藏 查看',
    continue: '继续',
    continue_title: '继续回复说明',
    continue_title_desc: `1.「继续」按钮作用：AI角色回复内容超过token上限时，点击「继续」AI续写未回复完的消息

2. 每次「继续」
（1）消耗💎时，会扣除当前聊天模式单次所需💎的50%（比如：当前模式下单次聊天600💎，「继续」则扣除300💎）
（2）消耗权益次数时，会扣除当前聊天模式1次的权益次数（权益次数最小单位1次，没有0.5次，故扣1次）

3. ‼️系统无法准确判断“文字”是否生成完毕，点击会扣费，请谨慎使用哦‼️`,
    continue_tips: `抱歉，当前模型（{{model}}）不支持继续回复功能，请切换到更高级模型再尝试~`
}
export default chat