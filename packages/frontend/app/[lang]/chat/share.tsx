
import { memo, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import Modal from '@share/src/ui/dialog/Modal';
import { useTranslation } from 'react-i18next';
import cn from 'classnames';
import Toast from '@share/src/ui/toast';
import type { FC, FormEvent } from 'react'
import AutoTextareaHeight from '@share/src/ui/dialog/CreateCard/AutoTextareaHeight';
import { useForm, SubmitHandler } from "react-hook-form"
import { getByteLength } from '@share/src/module/stringTool';
import useRequest from '@share/src/hook/useRequest';
import { Share2 } from 'lucide-react';
import useLocalStorage from '@share/src/hook/useLocalStorage';
import useDialogAlert from '@share/src/hook/useDialogAlert';
import useComfirm from '@share/src/hook/useComfirm';
import { LightBulbIcon } from '@heroicons/react/24/solid'
import Link from 'next/link';
import useSWR from 'swr'

type FormData = {
    title: string,
    desc: string
}
const Share = ({ roleId, conversationId, mode_type, chatList, isResponsing }: any) => {
    const { t } = useTranslation()
    const [showShare, setShowShare] = useState(false)
    const [errMsg, setErrMsg] = useState('');
    const request = useRequest()
    const dialogAlert = useDialogAlert()
    const comfirm = useComfirm()
    const [showShareTip, setShowShareTip] = useLocalStorage('show_share_tip', false)
    const [shareRemain, setShareRemain] = useState(0)
    const [isGetShareConfig, setIsGetShareConfig] = useState(false);
    const { data, error, isValidating } = useSWR(isGetShareConfig? '/user/share/count': null, request)

    const {
        register,
        handleSubmit,
        watch,
        reset,
        setValue,
        setError,
        clearErrors,
        trigger,
        formState: { errors },
    } = useForm<FormData>({ mode: 'onChange' })
    const formValues = watch();
    const onShare: SubmitHandler<FormData> = async (data) => {
        if(shareRemain >= 10) {
            Toast.notify({ type: 'info', message: t('app.share.share_limit') })
            return;
        }
        Toast.showLoading('');
        try {
            const res = await request('/user/share', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    role_id: roleId,
                    conversation_id: conversationId,
                    title: data.title,
                    description: data.desc
                })
            })
            console.log('res', res);
            if (res?.error_code === 0) {
                closeModal()
                dialogAlert.show({
                    title: t('app.common.tips'),
                    desc: t('app.share.share_success_desc'),
                    alertStatus: 2,
                    comfirmBtn: t('app.chat.comfirm')
                })
            } else {
                Toast.notify({ type: 'error', message: t('app.common.exec_err') + ': ' + res?.message})
            }
        } catch (e: any) {
            Toast.notify({ type: 'error', message: t('app.common.load_err') })
            setErrMsg(String(e?.message))
            console.error('share error:', e);
        } finally {
            Toast.hideLoading();
        }
    }
    // 提交前做一次全局检查并弹窗提示
    const handleFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // data?.data?.group_link必须不为空，才能开启是否入群判断
        if(!data?.data?.is_in_group && data?.data?.group_link) {
            Toast.notify({ type: 'info', message: t('app.share.not_in_group') })
            return;
        }
        handleSubmit((data) => {
            onShare(data);
        }, async (errors) => {
            console.log('errors', errors);
        })(e);
    };
    const showShareModal = async () => {
        setIsGetShareConfig(true)
        setShowShare(true)
    }
    useEffect(() => {
        if(isValidating) {
            Toast.showLoading("Loading");
        } else {
            Toast.hideLoading();
        }
    }, [isValidating])
    useEffect(() => {
        if(data) {
            setShareRemain(data?.data?.share_count)
        }
    }, [data])
    const generateDesc = async () => {
        const isShowGenerateDescTip = localStorage.getItem('isShowGenerateDescTip')
        if(!isShowGenerateDescTip) {
            const res = await comfirm.show({
                title: t('app.common.tips'),
                desc: t('app.share.generate_desc_tip'),
                comfirmBtn: t('app.chat.comfirm'),
                icon: <LightBulbIcon className='w-6 h-6 text-yellow-600' />,
                isShowIgnore: true
            })
            if (res?.ignore) {
                localStorage.setItem('isShowGenerateDescTip', '1')
            }
            if (!res?.confirm) {
                console.log('res', res);
                return;
            }
        }
        
        Toast.showLoading(t('app.share.loading1'));
        try {
            const res = await request('/share/description/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    mode_type: mode_type, //目前只支持单角色卡的分享
                    mode_target_id: roleId,
                    // 获取最后6条记录
                    message_ids: chatList.slice(-6).map((item: any) => item.message_id)
                })
            })
            if (res?.error_code === 0) {
                Toast.notify({ type: 'success', message: t('app.share.generate_success') })
                setValue('desc', res?.data?.description)
            } else {
                Toast.notify({ type: 'error', message: t('app.common.exec_err') })
            }
            Toast.hideLoading();
        } catch (e: any) {
            Toast.notify({ type: 'error', message: t('app.common.exec_err') })
            setErrMsg(String(e?.message))
            console.error('share error:', e);
        } finally {
            Toast.hideLoading();
        }
    }
   
    useEffect(() => {
        const showTips = async () => {
            if (chatList.length >= 20 && !isResponsing && showShareTip === false) {
                setShowShareTip(true)
                const res = await dialogAlert.show({
                    title: t('app.common.tips'),
                    desc: t('app.share.tip_desc'),
                    alertStatus: 2,
                    comfirmBtn: t('app.share.comfirm')
                })
                if(res) {
                    showShareModal()
                }
            }
        }
        showTips()
    }, [chatList, isResponsing, showShareTip, dialogAlert, setShowShareTip])
    const closeModal = () => {
        setShowShare(false);
        setIsGetShareConfig(false)
    }
    return (
        <>
            {chatList.length >= 10 && <button onClick={showShareModal} type="button" className="text-blue-500 fixed right-3 bottom-[calc(96px_+_env(safe-area-inset-bottom))]">
                <Share2 className='w-6 h-6' />
            </button>}
            {showShare && createPortal(<Modal isOpen={true} onClose={closeModal}>
                <div className='min-h-20 mb-2'>
                    <div className="flex min-h-full flex-1 flex-col justify-center px-6 pt-3">
                        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
                            <h2 className=" text-center text-xl font-bold leading-9 tracking-tight">
                                {t('app.share.title')}
                            </h2>
                        </div>

                        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
                            <form className="space-y-6" action="#" method="POST" onSubmit={handleFormSubmit}>
                                <div>
                                    <label htmlFor="title" className="block text-sm font-medium leading-6 ">
                                        {t('app.share.title1')}  {errors.title ? (
                                                <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.title.message}</span>) : <span className='text-xs mt-1'>*</span>
                                            }
                                    </label>
                                    <div className="mt-2">
                                        <input
                                            id="title"
                                            type="text"
                                            {...register("title", {
                                                required: t('app.dialog.require'), validate: {
                                                    len: (value: any) => {
                                                        const byteLength = getByteLength(value);
                                                        // console.log('byteLength', byteLength, value);
                                                        return byteLength <= 30 || t('app.dialog.name_len');
                                                    }
                                                }
                                            })}
                                            autoComplete="title"
                                            required
                                            placeholder={t('app.share.title_placeholder')}
                                            className="ipt py-2.5"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <div className="flex items-center justify-between">
                                        <label htmlFor="desc" className="block text-sm font-medium leading-6 ">
                                            {t('app.share.desc')} {errors.desc ? (
                                                <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.desc.message}</span>) : <span className='text-xs mt-1'>*</span>
                                            }
                                        </label>
                                        <div className="text-sm">
                                            <button type="button" onClick={generateDesc} className="font-semibold text-indigo-400 hover:text-indigo-500">
                                                {t('app.share.generate')}
                                            </button>
                                        </div>
                                    </div>
                                    <div className="mt-2">
                                        <AutoTextareaHeight value={formValues?.desc} className={'dark:bg-gray-800 dark:ring-gray-500'} placeholder={t('app.share.desc_placeholder')} id="desc" isRequired={true} {
                                            ...register("desc", {
                                                validate: {
                                                    len: (value: any) => {
                                                        const byteLength = getByteLength(value);
                                                        // console.log('byteLength', byteLength, value);
                                                        return byteLength <= 200 || t('app.share.desc_len');
                                                    }
                                                }
                                            })
                                        } />
                                    </div>
                                    {errMsg && <p className='text-xs mt-1 text-red-500'>{errMsg}</p>}
                                    {/* data?.data?.group_link存在值，才说明这个功能开启 */}
                                    {data?.data?.group_link && <div className='text-xs mt-1'>{t('app.share.join_group_desc')}{data?.data?.is_in_group? <span className='rounded ml-1 text-blue-500 '>{t('app.share.joined_group')}</span> :<Link href={data?.data?.group_link} className='text-blue-500 ml-1 underline' target='_blank'>{t('app.share.join_group')}</Link> }
                                    </div>}
                                </div>

                                <div className='px-2 py-2 flex flex-row-reverse'>
                                    <button className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' type='submit'>{t('app.share.comfirm1', { n: shareRemain })}</button>
                                    <button type='button' className='p-2 px-5 dark:bg-gray-800 bg-gray-300 rounded text-sm ' onClick={closeModal}>{t('app.common.cancel')}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </Modal>, document.body)}
        </>
    )
}


export default memo(Share, (prevProps, nextProps) => {
    // 
    return prevProps.chatList.length === nextProps.chatList.length && prevProps.isResponsing == nextProps.isResponsing;
  })