'use client'
import cn from 'classnames'
import type { ReactEventHandler, ReactNode } from 'react'
import React, { useContext, useEffect, useRef, useState } from 'react'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Toast from '@little-tavern/shared/src/ui/toast'
import { useForm, SubmitHandler, useController } from "react-hook-form"
import { AuthContext } from '@little-tavern/shared/src/authContext'
import Image from 'next/image'
import { Cog6ToothIcon, UserCircleIcon } from '@heroicons/react/24/solid'
import useCrop from '@little-tavern/shared/src/hook/useCrop'
import { useTranslation } from 'react-i18next'
import useLocalStorage from '@share/src/hook/useLocalStorage'
import { createPortal } from 'react-dom';
import Modal from '@share/src/ui/dialog/Modal'
import { switchLanguages } from '@little-tavern/shared/src/i18n/settings'
import { setLocaleOnClient } from '@little-tavern/shared/src/i18n/client'
import { usePathname } from 'next/navigation'
import useDialogAlert from '@share/src/hook/useDialogAlert'
import { Switch } from '@headlessui/react'
import Illustrate from '@share/src/ui/tooltip/illustrate'
import useLoadArchive from '@share/src/hook/useLoadArchive';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link'
import AudioPlay from '@share/src/ui/AudioPlay';
import {tgWeb} from '@/app/module/global';

export type ISetting = {
  onSelect: ReactEventHandler
}

type FormData = {
  selected_bg_index: number
  opacity: number
}
const INSIDE_ONLY = 'INSIDE_ONLY';
const ALL = 'ALL';
const subBtnCls = 'bg-purple-500 rounded px-2 ml-3 text-xs py-1 text-white';

const Setting = ({ className = '', modeType, chatInfo, groupId }: any) => {
  const { t, i18n } = useTranslation()
  const request = useRequest();
  const croper = useCrop();
  const auth = useContext(AuthContext)
  const user: any = auth?.user;
  const [isEdit, setIsEdit] = useState(false)
  const lang = i18n.language
  const pathname = usePathname()
  const [opacity, setOpacity] = useState(95)
  const dialogAlert = useDialogAlert()
  const [voice, setVoice] = useState(true)
  const [statusBlockSwitch, setStatusBlockSwitch] = useState(user?.status_block_switch)
  const loadArchive = useLoadArchive();
  const router = useRouter();
  const bgContainerRef = useRef(null)
  // console.log('statusBlockSwitch', statusBlockSwitch);
  // 获取群聊卡或者单卡角色
  const roles = chatInfo?.roles?.length > 0 ? chatInfo?.roles : [chatInfo]
  const roleIds = roles.map((item: any) => item.id || item.role_id).join(',')
  const [speakerInfo, setSpeakerInfo] = useState<any>(null)
  // console.log('chatInfo', chatInfo, roleIds, speakerInfo)
  const onSpeakerChange = async (speakerId: string, roleId: string) => {
    Toast.showLoading('')
    const res = await request('/user_role_config/update', { 
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        role_id: roleId,
        speaker_id: speakerId
      })
    });
    Toast.hideLoading()
    if(res.error_code === 0) {
      Toast.notify({ type: 'success', message: t('app.mine.update_success') })
      setSpeakerInfo((n: any) => {
        return {
          ...n,
          role_speaker_list: n.role_speaker_list.map((item: any) => {
            if(item.role_id === roleId) {
              return {
                ...item,
                speaker_id: speakerId
              }
            }
            return item
          })
        }
      })
    }
    
    // console.log('speakerId', speakerId)
  }

  // 是否展示切换提示
  const [showSwitchIdentityTip, setShowSwitchIdentityTip] = useLocalStorage('showSwitchIdentityTip', true)

  const change = (l: string) => {
    const searchStr = window.location.search;
    setLocaleOnClient(l)
    // 切换语言后，重新刷新页面，重置状态
    location.replace(pathname.replace(lang, l) + searchStr + '#reload=1')
  }
  const [chatBgList, setChatBgList] = useState<any>(user?.image_bgs || [])
  // 官方背景图片
  const [officialBgs, setOfficialBgs] = useState<any>(user?.official_bgs || [])
  
  const setLang = (e: any) => {
    change(e.target.value)
  }
  const {
    register,
    handleSubmit,
    watch,
    getValues,
    setValue,
    formState: { errors },
  } = useForm<FormData>()
  const updateSetting = async (data: any, isShowToast = true) => {
    Toast.showLoading('');
    const formData = new FormData()
    data.image_bgs = chatBgList;
    data.voice_content_type = voice ? INSIDE_ONLY : ALL
    data.status_block_switch = statusBlockSwitch
    formData.append('setting', JSON.stringify({
      ...data,
      opacity: opacity
    }));
    try {
      const res = await request(`/setting`, {
        method: 'POST',
        body: formData
      });
      auth?.updateUserInfo({
        image_bgs: res.image_bgs,
        selected_bg_index: res.selected_bg_index,
        opacity: res.opacity,
        voice_content_type: res.voice_content_type,
        status_block_switch: res.status_block_switch
      });
      isShowToast && Toast.notify({ type: 'success', message: t('app.mine.update_success') })
      Toast.hideLoading();
      setIsEdit(false)
    } catch (e) {
      console.error('Upload error:', e);
      isShowToast && Toast.notify({ type: 'success', message: t('app.mine.create_failed') })
      Toast.hideLoading();
    }
  }
  const onSubmit: SubmitHandler<FormData> = async (data) => {
    updateSetting(data)
  }

  const handleFileChange = async (e: any) => {
    if (chatBgList.length >= 2) {
      Toast.notify({
        type: 'info',
        message: t('app.mine.upload_limit_bg')
      })
      return;
    }
    const tmpFile = e.target.files[0];
    e.target.value = ''
    try {
      const file: any = await croper.show(URL.createObjectURL(tmpFile), 1, 0.9, 1500);
      Toast.showLoading('');
      const formData = new FormData()
      formData.append('category', 'CHAT_BACKGROUND');
      formData.append('img', file)
      try {
        const res = await request(`/tools/upload/img`, {
          method: 'POST',
          body: formData
        });
        if (res.error_code === 0) {
          Toast.notify({ type: 'success', message: t('app.mine.update_success') })
          setChatBgList([...chatBgList, res.data.img_url]);
        }
        Toast.hideLoading();
      } catch (e) {
        console.error('Upload error:', e);
        Toast.notify({ type: 'success', message: t('app.mine.create_failed') })
        Toast.hideLoading();
      }
    } catch (e) {
      console.log('handleFileChange e', e);
    }
  }
  const del = (index: number) => {
    setChatBgList(chatBgList.filter((_: any, i: number) => i !== index))
  }
  useEffect(() => {
    if (user) {
      setChatBgList(user?.image_bgs || [])
      setOfficialBgs(user?.official_bgs || [])
      setValue('selected_bg_index', user?.selected_bg_index || 0)
      setOpacity(user?.opacity || 95)
      setVoice(user?.voice_content_type === INSIDE_ONLY)
    }
  }, [user])
  const [profileList, setProfileList] = useState<any>([])
  const getUserInfo = async () => {
    Toast.showLoading('');
    try {
      const [res, speakerRes] = await Promise.all([request(`/user/alt_profile`), request(`/user_role_config?role_ids=${roleIds}`)]);
      if (res.error_code === 0) {
        if (res.data.alt_profile_list?.length > 0) {
          setProfileList(res.data.alt_profile_list.map((item: any) => ({
            ...item,
            enable: item.status === 2
          })))
          const userInfo = res.data.alt_profile_list.find((item: any) => item.status === 2)
          auth?.updateUserInfo({
            avatar: userInfo.avatar,
            nickname: userInfo.nickname
          })
        }
      }
      if(speakerRes.error_code === 0) {
        setSpeakerInfo(speakerRes.data)
      }
    } catch (e) {
      Toast.notify({ type: 'error', message: t('app.common.load_err') })
      console.error('getExtroInfo error:', e);
    } finally {
      Toast.hideLoading();
      setIsEdit(true)
    }
  }

  const showUser = () => {
    getUserInfo();
  }

  const selectUser = async (userInfo: any) => {
    if (showSwitchIdentityTip) {
      const res = await dialogAlert.show({
        title: t('app.mine.switch_identity_tip'),
        desc: t('app.mine.switch_identity_desc'),
        alertStatus: '2'
      })
      if (!res) {
        return;
      }
      setShowSwitchIdentityTip(false)
    }
    Toast.showLoading('');
    try {
      const res = await request(`/user/alt_profile/active?profile_id=${userInfo.id}`, {
        method: 'POST'
      })
      if (res.error_code === 0) {
        Toast.notify({ type: 'success', message: t('app.mine.switch_success') })
        auth?.updateUserInfo({
          avatar: userInfo.avatar,
          nickname: userInfo.nickname
        })
        // 更新profileList
        setProfileList(profileList.map((item: any) => {
          if (item.id === userInfo.id) {
            return {
              ...item,
              enable: true
            }
          } else {
            return {
              ...item,
              enable: false
            }
          }
        }))
      }
    } catch (e) {
      Toast.notify({ type: 'error', message: t('app.common.load_err') })
      console.error('getExtroInfo error:', e);
    } finally {
      Toast.hideLoading();
    }
  }

  // 背景图折叠逻辑
  const toggleBgList = () => {
    setBgListExpand(!bgListExpand)
  }
  const [bgListExpand, setBgListExpand] = useState(false)
  useEffect(() => {
    console.log('bgListExpand', bgListExpand)
    const bgContainerDom: any = bgContainerRef.current;
    if(bgContainerDom !== null && isEdit) {
      const w = bgContainerDom?.clientWidth;
      const num  = Math.floor(w / 64);
      const lis = bgContainerDom?.querySelectorAll('li');
      const hideLis = Array.from(lis).slice(num - 2, lis.length - 1);
      if(bgListExpand) {
        hideLis.forEach((li: any) => {
          li.style.display = 'block';
        })
      } else {
        hideLis.forEach((li: any) => {
          li.style.display = 'none';
        })
      }
    }
  }, [isEdit, bgListExpand])
  return <>
    <button className={`flex justify-between items-center px-3 rounded ${className}`} onClick={showUser}>
      <Cog6ToothIcon className='h-4 w-4 dark:text-gray-100 text-gray-600' />
    </button>
    {isEdit && createPortal(<Modal isOpen={true} onClose={() => { setIsEdit(false) }}><form action="#" method="POST" onSubmit={handleSubmit(onSubmit)}>
      <div className="px-1 pb-2 pt-1 flex flex-wrap gap-6 justify-center sm:justify-normal max-h-[90vh] overflow-auto">
        <div className="rounded-md w-full py-3 dark:border-gray-500 dark:bg-gray-900 bg-white">
          <div className='space-y-2 pb-1'>
            <div className="col-span-full flex items-center">
              <label htmlFor="subTags" className="block text-sm leading-6 mr-2">
                {t('app.mine.switch_language')}
              </label>
              <div className=''>
                <select className='text-center dark:bg-black bg-white dark:text-white' value={lang} onChange={setLang}>
                  {switchLanguages.map((l, index) => {
                    return (
                      <option key={l} value={l}>{t(`app.lang.${l}`)}</option>
                    )
                  })}
                </select>
              </div>
            </div>
            <div className="col-span-full flex items-start">
              <label htmlFor="subTags" className="block text-sm leading-6 mr-2">
                {t('app.mine.switch_identity')}
              </label>
              <div className=''>
                {
                  profileList.map((item: any, index: number) => {
                    return <div key={item.id} className='mb-2'><div className='text-sm font-medium leading-6 flex justify-between bg-gray-200 dark:bg-gray-800 items-center px-2'>
                      <div className='flex items-center flex-1'>
                        <span className='mr-1'>{index + 1}.</span>
                        <label htmlFor={`user.${index}.enable`} className="flex items-center mr-2 shrink py-1">
                          <input name='user' className='ml-1 mr-1' type='radio' id={`user.${index}.enable`} checked={item.enable} value={item.enable} onChange={() => selectUser(item)} />
                          <span className='block mr-1'>{item.nickname}</span>
                          {item.avatar ? <Image className='rounded-full h-8 w-8 object-cover' src={item.avatar} width={32} height={32} alt={'avatar'} /> : <UserCircleIcon className="h-8 w-8 text-gray-500 mr-1" aria-hidden="true" />}
                        </label>
                      </div>
                    </div>
                    </div>
                  })
                }
              </div>
              <Link href={`/${lang}/mine/gift/free?action=edit`} className={subBtnCls}>{t('app.common.go_setting')}</Link>
            </div>
            <div className="col-span-full flex items-center">
              <label htmlFor="subTags" className="block text-sm leading-6 mr-2">
                {t('app.mine.bg_transparent')}
              </label>
              <div className='flex items-center'>
                <input type="range" value={opacity} min="1" max="100" onChange={(e: any) => setOpacity(e.target.value)} />
                <span className='ml-2'>{opacity}%</span>
              </div>
            </div>
            <div className="">
              <div className='flex items-center mb-2'>
              <label htmlFor="photo" className="block text-sm leading-6">
                {t('app.mine.chat_bg')}
              </label>
              <label htmlFor="add_avatar_button" className="cursor-pointer inline-flex items-center">
                <div className={subBtnCls}
                >{t('app.common.upload')}</div>
                <input hidden type="file" onChange={handleFileChange} id="add_avatar_button" name="avatar" accept="image/*"></input>
              </label>
              </div>
              <div>
                <ul className='flex flex-wrap text-xs' ref={bgContainerRef}>
                  <li className='mr-2 mb-2'>
                          <label htmlFor={`bg-0`} className='block w-16 h-16 bg-[rgb(var(--background-end-rgb))] border border-gray-300 dark:border-gray-500'></label>
                          <div className='flex items-center space-x-0.5 justify-center mt-2'>
                            <input
                              id={`bg-0`}
                              type="radio"
                              {...register('selected_bg_index')}
                              value={0}
                              defaultChecked={0 === getValues('selected_bg_index')}
                            />
                          </div>
                  </li>
                  {
                    chatBgList.map((item: string, index: number) => {
                      return (
                        <li key={index} className='relative mr-2 mb-2'>
                          <label htmlFor={`bg-${index + 1}`} className='w-16 h-16 bg-[rgb(var(--background-end-rgb))]'>
                            <Image alt='chat bg' src={item} width={64} height={64} />
                          </label>
                          <div className='flex items-center space-x-0.5 justify-center mt-2'>
                            <input
                              id={`bg-${index + 1}`}
                              type="radio"
                              {...register('selected_bg_index')}
                              value={index + 1}
                              defaultChecked={index + 1 === getValues('selected_bg_index')}
                            />
                          </div>
                          <button type="button" onClick={() => { del(index) }} className='flex items-center w-3.5 h-3.5 justify-center text-white bg-purple-500 rounded-full absolute right-0.5 top-0.5'>-</button>
                        </li>
                      )
                    })
                  }
                  {
                    officialBgs.map((item: string, index: number) => {
                      const newIndex = index + 1 + chatBgList.length
                      return (
                        <li key={newIndex} className='relative mr-2 mb-2'>
                          <label htmlFor={`bg-${newIndex}`} className='w-16 h-16 bg-[rgb(var(--background-end-rgb))]'>
                            <Image alt='chat bg' src={item} width={64} height={64} />
                          </label>
                          <div className='flex items-center space-x-0.5 justify-center mt-2'>
                            <input
                              id={`bg-${newIndex}`}
                              type="radio"
                              {...register('selected_bg_index')}
                              value={newIndex}
                              defaultChecked={newIndex === getValues('selected_bg_index')}
                            />
                          </div>
                        </li>
                      )
                    })
                  }
                  <li className='w-16 h-16 flex items-center justify-center'>
                      <button onClick={toggleBgList} type='button' className="rounded-md dark:bg-gray-800 px-3 py-2.5 text-lg shadow-sm dark:hover:bg-gray-700 border border-gray-300 hover:bg-gray-200 text-sm"
                      >{bgListExpand? t('app.common.less') : t('app.common.more')}</button>
                  </li>
                </ul>
              </div>
            </div>
            <div className="flex items-center">
              <div className="block text-sm leading-6">
              {t('app.history.archive')}
              </div>
              <div>
                  <button type='button' className={subBtnCls} onClick={async () => {
                    const id = groupId || chatInfo.role_id;
                    const cardName = chatInfo.role_name || chatInfo.group_name;
                    const res: any = await loadArchive.show({ roleId: id, cardName: cardName, modeType })
                    if (res) {
                      const _cid = res?.cid ? res?.cid : '';
                      const searchParam = new URLSearchParams(location.search);
                      const fromUrl = encodeURIComponent(decodeURIComponent(searchParam.get('from') || ''));
                      const url = res?.isGroup ? `/${lang}/chat?groupid=${res?.id}&from=${fromUrl}&cid=${_cid}` : `/${lang}/chat?roleid=${res?.id}&from=${fromUrl}&cid=${_cid}`;
                      // console.log('url', url);
                      router.replace(url);
                      setIsEdit(false)
                    }
                  }}>
                    {t('app.history.load_archive')}
                  </button>
              </div>
            </div>
            <div className="pb-3">
              <div className="block text-sm leading-6 mb-2">
                {t('app.mine.voice')}
                <Illustrate title={t('app.mine.voice_title')} desc={t('app.mine.voice_desc')} className='ml-1'></Illustrate>
              </div>

              {!tgWeb && <div>
                <label htmlFor="voiceGenerate" className='flex items-center text-sm mb-2'>
                  <span>{t('app.mine.auto_generate_voice')}</span>
                  <Switch
                    id='voiceGenerate'
                    checked={voice}
                    onClick={() => {
                      setVoice(!voice)
                    }}
                    className="group inline-flex h-4 w-7 items-center rounded-full bg-gray-300 dark:bg-gray-500 transition data-[checked]:bg-purple-500 dark:data-[checked]:bg-purple-500 ml-1"
                  >
                    <span className="size-3.5 translate-x-0 rounded-full bg-white transition group-data-[checked]:translate-x-3.5" />
                  </Switch>
                </label>
              </div>}

              <div>
                <label htmlFor="statusBlockSwitch" className='flex items-center text-sm mb-2'>
                  <span>{t('app.common.status_setting')}</span>
                  <Illustrate title={t('app.common.status_setting_title')} desc={t('app.common.status_setting_desc')} className='ml-1 mr-1'></Illustrate>
                  <Switch
                    id='statusBlockSwitch'
                    checked={statusBlockSwitch}
                    onClick={() => {
                      setStatusBlockSwitch(!statusBlockSwitch)
                    }}
                    className="group inline-flex h-4 w-7 items-center rounded-full bg-gray-300 dark:bg-gray-500 transition data-[checked]:bg-purple-500 dark:data-[checked]:bg-purple-500 ml-1"
                  >
                    <span className="size-3.5 translate-x-0 rounded-full bg-white transition group-data-[checked]:translate-x-3.5" />
                  </Switch>
                </label>
              </div>

              {!tgWeb && <div className="space-y-1 max-w-fit">
                  {roles.map((role: any) => {
                    const roleSpeaker = speakerInfo?.role_speaker_list.find((speaker: any) => {
                      return speaker.role_id == (role.id || role.role_id)
                    });
                    const speakerSampleUrl: string = speakerInfo?.speaker_list.find((speaker: any) => speaker.speaker_id == roleSpeaker?.speaker_id)?.sample_url;
                    // console.log('role.id', role)
                    return <div key={role.role_id} className='col-button-full flex items-center text-sm'>
                      <div className='w-6 h-6 rounded-full overflow-hidden border mr-1'><Image src={role.role_avatar} alt={role.role_name} width={24} height={24} /></div>
                      <div className='mr-1 truncate flex-1'>{role.role_name}</div>
                      {/* <label htmlFor="speaker_id" className="text-sm leading-6 ">
                      {t('app.dialog.voice')}
                      </label> */}
                      <div className="ml-1 flex">
                      {speakerInfo?.speaker_list && <select onChange={(e: any) => {
                        const speakerId = e.target.value;
                        onSpeakerChange(speakerId, role.id || role.role_id);
                      }} className='w-28 dark:bg-gray-800 bg-gray-200 p-1 mr-2 outline-none' defaultValue={roleSpeaker?.speaker_id}>
                        {
                          speakerInfo?.speaker_list.map((speaker: any) => {
                            return <option className='w-24 p-1' key={speaker.speaker_id} value={speaker.speaker_id}>
                              {speaker.speaker_name}
                              </option>
                          })
                        }
                      </select>}
                      <AudioPlay url={speakerSampleUrl} />
                      </div>
                    </div>
                  })}
              </div>}
            </div>
            <button className='priBtn w-full p-1.5 px-3 bg-purple-500 text-white rounded text-sm' type="submit">{t('app.common.save')}</button>
          </div>
        </div>
      </div>
    </form></Modal>, document.body)}
  </>
}


export default Setting