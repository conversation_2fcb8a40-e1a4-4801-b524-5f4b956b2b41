import type { <PERSON>, MouseEventHandler } from 'react'
import { memo, useContext, useState } from 'react'
import cn from 'classnames'
import { useTranslation } from 'react-i18next';
import { User } from '@little-tavern/shared/src/authContext'
import Image from 'next/image'
import { IChat } from './type';
import useLightBox from '@little-tavern/shared/src/hook/useLightBox';
import { useTheme } from 'next-themes'
import Markdown from '../components/markdown';
import format from '@/app/module/formate-date';
import { GiftIcon, ArrowPathIcon, CameraIcon, ArrowUturnDownIcon } from '@heroicons/react/24/solid'
import AudioPlay from '@little-tavern/shared/src/ui/AudioPlay';
import s from './style.module.css'
import Toast from '@share/src/ui/toast';
import useRequest from '@share/src/hook/useRequest';
import useComfirm from '@share/src/hook/useComfirm';
import { AuthContext } from '@little-tavern/shared/src/authContext';
import useDialogAlert from '@share/src/hook/useDialogAlert';
import ModeChangeHistory from '../components/chat-input/modeChangeHistory';
import Photo from './components/photo';
import { Copy, SquarePen } from 'lucide-react';
import EditStatusBlock from './editStatusBlock'
import {tgWeb} from '@/app/module/global';

interface IAIChat {
  chat: IChat
  user?: User | null | undefined
  retry?: any
  chatList: IChat[]
  index: number
  chatInfo: any
  setChatList?: any
  setResponsing?: any
  isShowRevocation?: boolean
  revocation?: any,
  isShowContinue?: boolean
  onSend?: ({ aiRoleId }: { aiRoleId: any }) => void
  isChatSampleStyle?: boolean
  isBg?: boolean
  opacity?: number
  modelEventMap?: any
  // 是否展示图片重试等按钮，聊天历史不展示
  isImgRetry?: boolean
  setChatInfo?: any
}
const AIChat: FC<IAIChat> = ({ chat, retry, chatList, index, chatInfo, setChatList, setResponsing, isShowRevocation, revocation, onSend, isShowContinue, isChatSampleStyle, isBg, opacity, modelEventMap, isImgRetry, setChatInfo }) => {
  const { t } = useTranslation()
  const { resolvedTheme } = useTheme()
  const lightBox = useLightBox();
  const auth = useContext(AuthContext);
  const user = auth?.user
  const roles = [...(chatInfo?.roles || []), ...(chatInfo?.deleted_roles || [])];
  const role = roles.find((item: any) => {
    return item.id == chat.role_id
  })
  const roleName = role?.role_name || chatInfo.role_name
  const privateCard = chatInfo?.group_name ? role?.private_card : chatInfo.private_card
  const avatar = role?.role_avatar || chatInfo.role_avatar
  // 是否支持生图
  const supportPhoto = role?.support_photo || chatInfo.support_photo;
  const request = useRequest();
  const comfirm = useComfirm();
  const dialogAlert = useDialogAlert()
  const [openStatusBlock, setOpenStatusBlock] = useState(false)

  const getImg = async (msgId: string | undefined, version: string | undefined) => {
    const isCache = chatList.some((elem) => {
      return elem.loadingImg
    })
    if (isCache) {
      console.log('isCache', isCache);
      Toast.notify({
        type: 'info',
        message: t('app.chat.one_img_tips')
      })
      return;
    }
    const showChatImgTip = localStorage.getItem('isShowChatImgTip');
    if (showChatImgTip != '1') {
      const res = await comfirm.show({
        title: t('app.chat.img_title'),
        desc: t('app.chat.img_desc'),
        comfirmBtn: t('app.chat.img_confirm_btn'),
        showCancelBtn: true,
        icon: <GiftIcon className="h-6 w-6 text-red-400" />,
        cancelBtn: t('app.chat.img_cancel_btn'),
        isShowIgnore: true
      })
      if (res?.ignore) {
        localStorage.setItem('isShowChatImgTip', '1')
      }
      if (!res?.confirm) {
        console.log('res', res);
        return;
      }
    }

    // setResponsing(true)
    setChatList((draft: any) => {
      draft[draft.length - 1].loadingImg = true
    })
    const len = chatList.length;
    try {
      const res = await request(`/take_photo`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message_id: msgId,
          version: version
        })
      })
      // setResponsing(false)
      console.log('hatList[chatList.length - 1].loadingImg', chatList[chatList.length - 1].loadingImg);
      setChatList((draft: any) => {
        draft[len - 1].loadingImg = false;
        draft[len - 1].photo_url = res.photo_url;
        draft[len - 1].photo_id = res.photo_id;
      })
      console.log('getImg', res);
    } catch (e: any) {
      // 其他网络情况处理
      if(e.status != 402) {
        Toast.notify({
          type: 'error',
          message: t('app.chat.content_err', {status: e.status || e.name, errMsg: e.statusText || e.message})
        })
      }
      setResponsing(false)
      setChatList((draft: any) => {
        draft[draft.length - 1].loadingImg = false;
      })
      console.log("fetch err", e);
    }
  }
  const copy = async (content: string) => {
    // Remove HTML tags from content
    const cleanContent = content.replace(/<[^>]*>/g, '');
    navigator.clipboard.writeText(cleanContent).then(
      function () {
        Toast.notify({
          type: 'success',
          message: t('app.chat.copy_success')
        })
      },
      function () {
        Toast.notify({
          type: 'error',
          message: t('app.chat.copy_fail')
        })
      },
    );
  }
  const [statusInfo, setStatusInfo] = useState<any>({})
  const edit = async (content: string) => {
    Toast.showLoading('');
    try {
      const res = await request(`/user/chat/status_block/info?conversation_id=${chatInfo.conversation_id}&message_id=${chat.message_id}&version=${chat.version}`)
      console.log('edit', res);
      if(res.error_code === 0) {
        setStatusInfo(res.data)
        setOpenStatusBlock(true)
      } else {
        throw new Error(res.message)
      }
    } catch (error: any) {
      Toast.notify({
        type: 'error',
        message: t('app.chat.content_err', {status: error.status || error.name, errMsg: error.statusText || error.message})
      })
      console.log('edit err', error);
    } finally {
      Toast.hideLoading();
    }
    
  }
  const bgColor = resolvedTheme === 'dark' ? `rgb(17 24 39 / ${opacity ? opacity : 95}%)` : `rgba(255 255 255 / ${opacity ? opacity : 95}%)`;
  return <>
    <div className={cn('relative mt-4 flex max-w-[95%] sm:max-w-[86%] items-start', isChatSampleStyle && '!mt-2 sm:!max-w-[95%]')}>
      <div className='relative w-12 ml-2 mr-2 rounded'>
        <Image className={cn('rounded w-12 h-[72px] max-w-none dark:bg-gray-800 bg-gray-200 object-cover', isChatSampleStyle && '!rounded-full !h-10 !w-10')} src={avatar || '/dot.png'} width={64} height={96} alt={roleName || ''} onClick={(e) => { !isChatSampleStyle && avatar && lightBox.show({ src: avatar }); }} />
        <h2 className='w-full text-center text-xs mt-1 dark:text-gray-300 text-gray-500 break-all'>{roleName}</h2>
        {privateCard && <p className='absolute w-full top-[56px] bg-black-500/25 rounded-b text-[10px] text-center leading-4 dark:text-white/50 text-white'>{t('app.chat.private_card')}</p>}
      </div>
      <div className={cn(s.shadow, 'sm:mr-5 dark:bg-gray-900 bg-white  pb-1 rounded-lg relative z-0', isChatSampleStyle? '!bg-transparent' : 'p-3')} style={isBg?{
        backgroundColor: `${bgColor}`
      }: {}}>
        <div className={cn(chat.isLoading && s.loader, resolvedTheme === 'dark' && s.loaderDark, 'prose dark:prose-invert relative text-[15px] relative z-0', isChatSampleStyle && s.lineClamp2, isChatSampleStyle && '!text-sm')}>
          {privateCard && <div data-before={t('app.chat.person_card')} data-after={t('app.chat.person_limit')} className={cn(s.watermarked, `before:!content-[attr(data-before)] after:!content-[attr(data-after)]`, "w-full h-full absolute -z-10 top-0 left-0")}></div>}
          <Markdown content={chat.content} formateType={chatInfo?.chat_type} roleName={roleName} regs={user?.regex_rules?.filter((reg: any) => {
            return reg.affects.includes('AI_OUTPUT')
          })} />
        </div>
        {chat.isContinueLoading && <div className={cn(s.loader, resolvedTheme === 'dark' && s.loaderDark, 'w-12')}></div>}
        {chat.can_continue_replay && isShowContinue && (chatList.length - 1) === index && <div className='text-sm'>
          <span className='text-gray-500 text-xs mr-1'></span><button className='bg-purple-500 rounded px-2.5 py-1 text-white' type='button' onClick={async () => {
            // haiku模型不支持续答
            // if(user?.user_model === 'm1') {
            //   const model = user?.chat_products?.find((model) => {return model.mid === 'm1'})
            //   dialogAlert.show({
            //     title: t('app.common.tips'),
            //     alertStatus: '2',
            //     desc: t('app.chat.continue_tips', {model: model.model_name})
            //   })
            //   return;
            // }
            const isShowContinueAnswerTips = localStorage.getItem('isShowContinueAnswerTips1')
            if(!isShowContinueAnswerTips) {
              const res = await comfirm.show({
                title: t('app.chat.continue_title'),
                showIcon: false,
                desc: t('app.chat.continue_title_desc'),
                isShowIgnore: true
              })
              if (res?.ignore) {
                localStorage.setItem('isShowContinueAnswerTips1', '1')
              }
              if (!res?.confirm) {
                return;
              }
            }
            onSend && onSend({aiRoleId: chat.role_id})
            }}>{t('app.chat.continue')}</button>
        </div>
        }
        <div className='flex mt-2 justify-end text-xs text-gray-500 items-center'>
          {chatList.length !== 1 && !chat.isRecieving && isShowRevocation && user?.status_block_switch && chat.message_id && <button onClick={() => {
            edit(chat.content)
          }} className='p-2 dark:hover:text-white flex'>
            <SquarePen className="h-4 w-4 text-blue-400 mr-0.5" />
          </button>}
          {!chat.isRecieving && !isChatSampleStyle && <button onClick={() => {
            copy(chat.content)
          }} className='p-2 dark:hover:text-white flex'>
            <Copy className="h-4 w-4 text-blue-400 mr-0.5" />
            {/* {t('app.chat.copy')} */}
          </button>}
          {!chat?.photo_url && chat.message_id && chatList.length !== 1 && !chat.isRecieving && (chatList.length - 1) === index && retry && supportPhoto && <button onClick={() => { getImg(chat.message_id, chat.version) }} className='mr-1 dark:hover:text-white flex'>
            <CameraIcon className="h-4 w-4 text-blue-400 mr-0.5" />
            {/* {t('app.chat.img')} */}
          </button>}
          {chatList.length !== 1 && !chat.isRecieving && isShowRevocation && revocation && <button onClick={() => { 
            revocation(chat.message_id)
            }} className='p-2 dark:hover:text-white flex'>
            <ArrowUturnDownIcon className="h-4 w-4 text-blue-400 mr-0.5" />
            {/* {t('app.chat.recall')} */}
          </button>}
          {chatList.length !== 1 && !chat.isRecieving && (chatList.length - 1) === index && retry && <button onClick={() => { retry(chat?.message_id, chat.role_id) }} className='p-2 dark:hover:text-white flex'>
            <ArrowPathIcon className="h-4 w-4 text-blue-400 mr-0.5" />
            {/* {t('app.chat.retry')} */}
          </button>}
          {!isChatSampleStyle && !tgWeb && (chat.message_id || chat.voice_url) && !chat.isRecieving && <AudioPlay url={chat.voice_url} msgId={chat.message_id} version={chat.version} />}
          {/* {chat.timestamp && <div>{format(chat.timestamp * 1000, 'MM/DD HH:mm')}</div>} */}
        </div>
        {/* 私有卡增加提示 */}
        {privateCard && chat?.content.length > 18 && <>
        <div className='opacity-50 absolute bottom-0 left-0 rounded-bl-lg rounded-tr-lg px-1  bg-red-500 text-xs text-white'>{t('app.chat.private')}</div>
        <div className='opacity-50 absolute top-0 right-0 rounded-bl-lg rounded-tr-lg px-1  bg-red-500 text-xs text-white'>{t('app.chat.private')}</div>
        </>}
      </div>
    </div>
    
    <Photo chat={chat} isChatSampleStyle={isChatSampleStyle} isBg={isBg} bgColor={bgColor} resolvedTheme={resolvedTheme} t={t} s={s} setChatList={setChatList} chatList={chatList} isImgRetry={isImgRetry} chatInfo={chatInfo} setChatInfo={setChatInfo} />
    <ModeChangeHistory msgId={chat.message_id} modelEventMap={modelEventMap} />
    {openStatusBlock && <EditStatusBlock statusInfo={statusInfo} isOpen={openStatusBlock} onClose={() => {setOpenStatusBlock(false)}} chatInfo={chatInfo} chat={chat} setChatList={setChatList} />}
  </>
}

export default memo(AIChat, (prevProps, nextProps) => {
  // 只有当 content 不同时，message_id不同时，并且是最后两条时（倒数第一条更新重试）才重新渲染
  return prevProps.chat.content === nextProps.chat.content && prevProps.chat.isRecieving === nextProps.chat.isRecieving && prevProps.chat.message_id == nextProps.chat.message_id && (prevProps.isShowRevocation === nextProps.isShowRevocation) && (prevProps.chat.loadingImg === nextProps.chat.loadingImg && prevProps.chat.can_continue_replay === nextProps.chat.can_continue_replay) && (prevProps.isBg === nextProps.isBg) && (prevProps.opacity === nextProps.opacity) && (prevProps.chat.retry_photos?.length === nextProps.chat.retry_photos?.length);
})