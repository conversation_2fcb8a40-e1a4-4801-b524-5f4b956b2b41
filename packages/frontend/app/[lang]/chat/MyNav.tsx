'use client'
import React, { Fragment, useContext, memo, useEffect, useState, useCallback } from 'react'
import { ArrowUturnLeftIcon, CheckCircleIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import Setting from './setting';
import { useBackButton } from '@share/src/hooks/useBackButton';
import IconClean from './icons/clean'
import useComfirm from '@share/src/hook/useComfirm';
import { DatabaseBackup } from 'lucide-react';
import useRequest from '@share/src/hook/useRequest';
import Toast from '@share/src/ui/toast';
import useDialogAlert from '@share/src/hook/useDialogAlert';
import { useRouter, useParams } from 'next/navigation'

const btnClass = 'group flex w-full items-center gap-2 rounded py-3 !px-1 dark:data-[focus]:bg-white/10 dark:hover:bg-white/10 hover:bg-violet-500 hover:text-white'
const iconClass = 'size-4 text-purple-500 dark:text-gray-300 group-hover:text-white'

const MyNav = ({ modeType, groupId, chatInfo, cardName, startNewChat }: any) => {
    const { t } = useTranslation()
    const comfirm = useComfirm();
    const dialogAlert = useDialogAlert();
    const { backHandler } = useBackButton({ useBackFn: true });
    const request = useRequest();
    const router = useRouter();
    const params = useParams()
    const lang = params.lang as string

    const onBackupCard = async () => {
        const res = await comfirm.show({
            title: t('app.chat.backup_card_title'),
            desc: t('app.chat.backup_card_desc'),
            showIcon: false,
            comfirmBtn: t('app.chat.backup_card_comfirm'),
            cancelBtn: t('app.chat.backup_card_cancel')
        })
        if (res?.confirm) {
            Toast.showLoading(t('app.chat.snapshot_generate_ing'));
            try {
                const bakcRes = await request(`/user/chat/snapshot/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        role_id: chatInfo?.role_id,
                        group_id: chatInfo?.group_id,
                        conversation_id: chatInfo?.conversation_id
                    })
                });
                if (bakcRes.error_code === 0) {
                    const res = await dialogAlert.show({
                        title: t('app.common.execute_success'),
                        desc: t('app.chat.backup_card_success'),
                        alertStatus: '2',
                        comfirmBtn: t('app.chat.go_view')
                    })
                    if(res) {
                        router.push(`/${lang}/mine/card/snapshoot`)
                    }
                } else {
                    throw new Error(bakcRes.error_code + ':' + bakcRes.message)
                }
            } catch (e: any) {
                console.error('backup-card error:', e);
                Toast.notify({
                    type: 'error',
                    message: t('app.common.exec_err') + ': ' + e?.message
                })
            } finally {
                Toast.hideLoading();
            }
        }
    }

    return <nav className='fixed dark:bg-[var(--background)] bg-white drop-shadow-u dark:drop-shadow-none z-10 w-full left-0 top-0 py-2 h-11 dark:border-b dark:border-[#2c2e2d]'>
        <button className='absolute p-2 py-2.5 left-1 top-0.5 flex text-sm items-center hover:bg-gray-200 dark:hover:bg-gray-800 rounded' onClick={backHandler}>
            <ArrowUturnLeftIcon className='w-4 h-4 mr-0.5 text-purple-500' />
            {t('app.common.save_back')}
        </button>
        <div className='w-fit mx-auto text-sm h-7 flex items-center justify-center'>
            <div className='flex items-center flex-shrink-0 text-gray-600 dark:text-gray-200'>
                <div className="text-center text-sm w-[50vw] truncate">
                    {cardName}
                </div>
            </div>
        </div>
        <div className='!absolute right-0 top-0 flex items-center'>
            <button className={btnClass} onClick={async () => {
                const res = await comfirm.show({
                    title: t('app.chat.warn'),
                    desc: t('app.chat.warn_desc')
                })
                if (res?.confirm) {
                    startNewChat()
                }
            }}>
                <div className={iconClass}><IconClean /></div>
            </button>
            {modeType === 'single' && <button className={btnClass} onClick={onBackupCard}><DatabaseBackup className='h-4 w-4 dark:text-gray-100 text-gray-600' /></button>}
            <Setting className={`${btnClass} !mr-1`} chatInfo={chatInfo} modeType={modeType} groupId={groupId} />
        </div>
    </nav>
}


export default memo(MyNav)
