import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";
import useComfirm from "@share/src/hook/useComfirm";

let isShowComfirm = true;
const Queue = ({queueInfo}: any) => {
  const { lang } = useParams()
  const { t } = useTranslation()
  const router = useRouter()
  const person_ahead = queueInfo?.person_ahead || 0;
  const left_wait_time = queueInfo?.left_wait_time || 0;
  const onePersonTime = Math.floor(left_wait_time / person_ahead);
  const [remainTime, setRemainTime] = useState(0)
  const [remainPerson, setRemainPerson] = useState(0)
  const comfirm = useComfirm();
  // console.log('remainTime', remainTime, remainPerson);

  const goToPay = () => {
    router.push(`/${lang}/pay?target_product_id=${queueInfo?.recharge_product_id}`)
  }
  const showQueueDialog = async (person_ahead: number, left_wait_time: number) => {
    const res = await comfirm.show({
      title: t('app.chat.queue_dialog_title'),
      desc: t('app.chat.queue_dialog_desc', {remainPerson: person_ahead, remainTime: left_wait_time}),
      comfirmBtn: t('app.chat.queue_dialog_comfirm_btn'),
      cancelBtn: t('app.chat.queue_dialog_cancel_btn'),
    })
    if (res?.confirm) {
      goToPay()
    }
  }
  useEffect(() => {
    // console.log('queueInfo', queueInfo);
    setRemainTime(left_wait_time)
    setRemainPerson(person_ahead)
    const itv = setInterval(() => {
      setRemainTime(prevTime => {
        const newRemainTime = prevTime - 1;
        setRemainPerson(prevPerson => {
          const newRemainPerson = Math.ceil(newRemainTime / onePersonTime) - 1;
          return newRemainPerson < 1 ? 1 : newRemainPerson;
        });
        if (newRemainTime <= 0) {
          clearInterval(itv);
          return 0;
        }
        return newRemainTime;
      });
    }, 1000)
    return () => {
        clearInterval(itv)
    }
  }, [queueInfo])
  useEffect(() => {
    // console.log('showQueueDialog', queueInfo);
    isShowComfirm && showQueueDialog(person_ahead, left_wait_time);
    isShowComfirm = false
  }, [])
  return (
    <>
    {remainTime > 0? <div className="text-center text-xs text-gray-300">
        <div>{t('app.chat.queue_desc', {remainPerson, remainTime})}</div>
        <button type="button" className="ml-1 bg-purple-500 text-white mt-1 px-1 py-0.5 rounded" onClick={() => {goToPay()}}>{t('app.chat.queue_dialog_comfirm_btn')}</button>
    </div>: <div className="text-center text-xs text-gray-300 mt-">{t('app.chat.queue_end')}</div>}
    </>
  )
}

export default Queue