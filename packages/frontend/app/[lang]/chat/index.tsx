'use client'
import React, { Suspense, useCallback, useContext, useEffect, useRef, useState } from 'react';
import s from './style.module.css'
import cn from 'classnames'
import AudioPlay, { stopAudio } from '@little-tavern/shared/src/ui/AudioPlay';
import Loader from '@little-tavern/shared/src/ui/Loading';
import { Trans, useTranslation } from 'react-i18next'
import { useParams, useSearchParams, useRouter } from 'next/navigation'
import ChatInput from '@/app/[lang]/components/chat-input'
import Toast from '@little-tavern/shared/src/ui/toast'
import { useLayoutEffect } from 'react';
import useRequest, { RequestStatus } from '@little-tavern/shared/src/hook/useRequest'
import { AuthContext } from '@little-tavern/shared/src/authContext';
import scrollToPosition from '@little-tavern/shared/src/hook/useScrollToBottom';
import { commonHeader } from '@little-tavern/shared/src/module/commonHeader';
import MyNav from './MyNav';
import './style.scss'
import * as Sentry from "@sentry/nextjs";
import ExportHistory from './exportHistory';
import useDialogLogin from '@little-tavern/shared/src/hook/useDialogLogin';
import { LoginStatus } from '@little-tavern/shared/src/authContext';
import Intro from './intro';
import UserChat from './userChat';
import AIChat from './AIChat';
import { IChat } from './type';
import { initUserChatTmp, initAIChatTmp, updateChatRes } from './tool';
import { useImmer } from 'use-immer';
import useComfirm from '@share/src/hook/useComfirm';
import Share from './share';
import { LoadStatus } from './tool';
import { isTGIOS } from '@little-tavern/shared/src/module/global';
import useDialogAlert from '@share/src/hook/useDialogAlert';
import { web } from '@/app/module/global'
import useLocalStorage from '@share/src/hook/useLocalStorage';
import Queue from './components/queue'

let controller: any;
// 每一轮对话，出现乱码自动限制重试一次
let canAutoRetry = true;
// 虚拟msgid，当服务端没有的时候，用这个，用于modelEventMap映射
let localMsgId = 10000;
const Chat = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const [loadHistory, setLoadHistory] = useState(true)
  const [scrollStatus, setScrollStatus] = useState('fast')
  const [isResponsing, setResponsing] = useState(false)
  const { notify } = Toast
  const [controlFocus, setControlFocus] = useState(0)
  const elementRef = useRef<HTMLDivElement>(null)
  const [chatList, setChatList] = useImmer<IChat[]>([]);
  const request = useRequest();
  // 默认聊第一个角色
  const [roleId, setRoleId] = useState<number>(-1);
  const [groupId, setGroupId] = useState<number>(0);
  const [chatInfo, setChatInfo] = useImmer<any>({})
  const [modeType, setModeType] = useState(groupId === 0 ? 'single' : 'group')
  const auth = useContext(AuthContext);
  const user = auth?.user
  const loginStatus = auth?.loginStatus;
  const uId = user?.id || '';
  const autoScrollRef = useRef(true);
  const dialogLogin = useDialogLogin();
  const confirm = useComfirm();
  const [supportMids, setSupportMids] = useState<string[]>([]);
  const dialogAlert = useDialogAlert()
  const router = useRouter()
  // 用于记录当前聊天次数，触发聊天完后的组件更新
  const [chatTimes, setChatTimes] = useLocalStorage('chatTimes', 0)
  // 是否要暂时排队提示
  const [queueInfo, setQueueInfo] = useState<any>(null)
  // 当前回复是否可以续写
  // const [can_continue_replay, setcan_continue_replay] = useState(false)
  const scrollBottom = (scrollStatus: string) => {
    // android tg需要在body滚动，非body滚动容易触发关闭窗口事件
    // const container = auth?.isAndroidTg ? document.documentElement : elementRef.current;
    const container = elementRef.current;
    if (container && (scrollStatus == 'fast' || scrollStatus == 'smooth')) {
      const scrollDistant = container.scrollHeight;
      if (scrollStatus == 'fast') {
        container.scrollTop = scrollDistant
      } else {
        scrollToPosition(container, scrollDistant)
      }
    }
  }
  // 首次进入滚动到底部逻辑
  useLayoutEffect(() => {
    scrollBottom(scrollStatus);
  }, [chatList, scrollStatus])

  // 限制聊天历史长度，导出聊天记录
  const [showExoprt, setShowExport] = useState('')
  const updateChatList = (draft: any) => {
    const n = 50;
    if (draft.length > n) {
      draft.splice(0, draft.length - n)
      setShowExport(t('app.chat.show_all_chats', { n: n }))
    } else {
      setShowExport('')
    }
    return draft;
  }
  const [modelEventMap, setModelEventMap] = useState<any>({})
  // newStart=0，读取聊天历史，newStart=1，新开存档
  const fetchHistory = async (_roleId = roleId, _groupId = groupId, newStart = 1, conversationId = '', sid = '') => {
    try {
      setLoadHistory(true);
      const _modeType = _groupId === 0 ? 'single' : 'group';
      const sidQuery = sid ? `&user_snapshot_id=${sid}` : '';
      const data = await request(`/chat/history?role_id=${_roleId}&conversation_id=${conversationId}&new_start=${newStart}&group_id=${_groupId}&mode_type=${_modeType}${sidQuery}`);
      setChatInfo(data);
      const list = updateChatList(data.chat_list);
      setChatList(() => list);
      setModeType(_modeType);
      setSupportMids(data?.support_product_ids || [])
      setModelEventMap(data?.model_event_map || {})
    } catch (error) {
      console.error('Failed to fetch chat history:', error);
      notify({ type: 'error', message: t('app.common.load_err') });
    } finally {
      setLoadHistory(false);
      setControlFocus(Date.now());
    }
  };
  useEffect(() => {
    return () => {
      stopAudio();
    }
  }, [])


  const searchParam = useSearchParams();
  useEffect(() => {
    if (loginStatus === LoginStatus.failed) {
      dialogLogin.show();
      return
    }
    if (!uId) return;
    const queryRoleId = parseInt(searchParam.get('roleid') || '0');
    const queryGroupId = parseInt(searchParam.get('groupid') || '0');
    const conversationId = searchParam.get('cid') || '';
    const newStart = parseInt(searchParam.get('new_start') || '0');
    // 快照id
    const sid = searchParam.get('sid') || '';
    setRoleId(queryRoleId);
    setGroupId(queryGroupId);
    fetchHistory(queryRoleId, queryGroupId, newStart, conversationId, sid);

  }, [loginStatus, uId, searchParam])

  const continueHandler = ({ aiRoleId }: any) => {
    // 隐藏继续按钮
    setChatList(draft => {
      const len = draft.length - 1
      draft[len].can_continue_replay = false;
      draft[len].isContinueLoading = true;
    })
    getReply({ isContinue: true, aiRoleId });
  }

  // 获取最新的有效的msg,如果最新的有问题，往前回溯，直到获取最新的msg.chatStatus !== false && msg.message_id的item
  const getLastMsg = (chatList: IChat[]): IChat => {
    let msg = { content: '' };
    // 如果最新的内容返回失败，往前回溯，直到找到有效消息
    // 从倒数第二条开始往前搜索
    for (let i = chatList.length - 1; i >= 0; i--) {
      if (chatList[i]?.chatStatus !== false && chatList[i]?.message_id) {
        msg = chatList[i];
        break;
      }
    }
    return msg;
  }

  const getReply = async ({ message, isRetry, msgId, aiRoleId, isContinue, lastMsgId, lastMsgVersion, auto_retry = false }: any) => {
    setResponsing(true)
    setScrollStatus('smooth')
    const lastMsg = getLastMsg(chatList);
    // lastMsgId 是最新一条成功输出的消息id
    lastMsgId = lastMsgId ? lastMsgId : lastMsg?.message_id || '';
    try {
      controller = new AbortController();
      let response: any = await fetch(`${process.env.NEXT_PUBLIC_API_HOST}/chat/replay`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...commonHeader
        },
        signal: controller.signal,
        body: JSON.stringify({
          role_id: aiRoleId || roleId,
          conversation_id: chatInfo?.conversation_id,
          message: message,
          isRetry: isRetry,
          mode_type: modeType,
          group_id: groupId,
          retry_message_id: msgId ? ((msgId.startsWith('local-') ? '' : msgId) || '') : '',
          language: lang,
          chat_continue: isContinue,
          last_message_id: lastMsgId && lastMsgId.startsWith('local-') ? '' : lastMsgId,
          last_message_version: String(lastMsgVersion ? lastMsgVersion : lastMsg?.version || ''),
          auto_retry: auto_retry,
          api_version: 'v2'
        })
      });
      const _msgId = response.headers.get('Message-Id');
      console.log("response.status", response.status);
      if (response.status == 402) {
        auth?.showBalanceConfirm();
        setResponsing(false)
        rollback();
        return;
      }
      if (response.status == 200) {
        // 设置上一个msg用户的msgid，只有倒数第二轮是用户，才设置msgid
        setChatList(draft => {
          const humanMsgId = response.headers.get('Human-Message-Id');
          if (draft.length >= 2 && draft[draft.length - 2].type == 'human' && humanMsgId) {
            draft[draft.length - 2].message_id = humanMsgId;
          }
          draft[draft.length - 1].message_id = _msgId || `local-${++localMsgId}`;
          draft[draft.length - 1].version = response.headers.get('Message-Version');
        })
        const reader = response.body.getReader();
        // 续答继续上一轮内容
        let result = isContinue ? chatList[chatList.length - 1].content : '';
        let needRetry = false;
        // 是否成功输出所有内容
        let chatStatus = false;
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            break;
          }
          let buffer = new TextDecoder("utf-8").decode(value);
          if (buffer.includes('\r\n\r\n')) {
            const lines = buffer.split('\r\n\r\n')
            try {
              lines.forEach((message) => {
                if (message && message.startsWith('event: end')) {
                  let s = message.substring(10);
                  s = s.split('data: ').join('')
                  try {
                    const endData = JSON.parse(s);
                    // 续答
                    if (endData.finish_reason === 'length') {
                      setChatList(draft => {
                        draft[draft.length - 1].can_continue_replay = true;
                      })
                    }
                    // console.log('endData', endData);
                    if (endData.need_retry && canAutoRetry) {
                      canAutoRetry = false;
                      const errMsg = endData.error;
                      if (errMsg) {
                        Toast.notify({
                          type: 'info',
                          message: errMsg
                        })
                      }
                      needRetry = true;
                    } else {
                      canAutoRetry = true;
                    }
                    // 排队
                    if(endData.error_code === 1006) {
                      setQueueInfo(endData.extra_data)
                    } else {
                      setQueueInfo(null)
                    }
                    if (endData.success) {
                      chatStatus = true;
                    }
                  } catch (e: any) {
                    console.log('parse event end data err');
                    Sentry.captureException(e);
                  }
                  return
                }
                if (!message || !message.startsWith('event: data\r\ndata: '))
                  return
                let s = message.substring(13);
                s = s.split('data: ').join('')
                result += s
              })
            }
            catch (e) {
              console.error('handleStream error', e)
            }
          }
          // console.log('result', result);
          setChatList(draft => {
            const len = draft.length - 1
            draft[len].content = result,
            draft[len].isLoading = false;
            draft[len].isContinueLoading = false;
            draft[len].isRecieving = true;
            draft[len].chatStatus = chatStatus;
          })
          handleScroll()
        }
        setResponsing(false)
        process.env.NEXT_PUBLIC_RM_CONSOLE != "true" && console.log('result-服务端返回的原始数据', result);
        setChatList(draft => {
          updateChatRes(draft[draft.length - 1]);
        })
        // 如果内容存在需要重试，触发重试
        if (needRetry) {
          retry(_msgId, aiRoleId, true, true);
        }
        getSpringFestival();
        setChatTimes(chatTimes + 1)
      } else {
        setResponsing(false)
        // 有状态码或异常说明的错误
        const errMsg = await response.text()
        if (response.status || errMsg) {
          const err = t('app.chat.content_err', { status: response.status, errMsg: errMsg });
          setChatList(draft => {
            const _draft = updateChatRes(draft[draft.length - 1]);
            _draft.content = err;
            _draft.message_id = response.headers.get('Message-Id') || '';
            _draft.version = response.headers.get('Message-Version') || '';
          })
          Sentry.captureException(err);
        }
      }
    } catch (e: any) {
      console.log("chat net err", e, roleId, uId);
      // 其他网络情况处理
      if (e.name !== 'AbortError') {
        setResponsing(false)
        let shouldShowToast = false;
        setChatList(draft => {
          const _draft = updateChatRes(draft[draft.length - 1]);
          // 如果有内容返回，保留，只弹toast
          if (_draft && _draft.content?.length > 0) {
            shouldShowToast = true;
          } else {
            _draft.content = t('app.chat.net_err');
          }
        })
        if (shouldShowToast) {
          Toast.notify({
            type: 'error',
            message: t('app.common.load_err')
          });
        }
        Sentry.captureException(e);
      }
    }
  }
  const retrySendUserChat = async ({ message, msgId }: any) => {
    const placeholderAnswerItem = initAIChatTmp()
    setChatList(draft => {
      draft.push(placeholderAnswerItem)
      updateChatList(draft);
    })
    getReply({ message: '', msgId: '', isRetry: false })
  }
  const sendUserChat = ({ aiRoleId, message, isRetry, msgId, lastMsgId }: any) => {
    const data: any = initUserChatTmp(message);
    return new Promise(async (resolve, reject) => {
      setChatList(draft => {
        draft.push(data)
        updateChatList(draft)
      })
      setResponsing(true)
      // console.log('sendUserChat chatList', chatList);
      try {
        controller = new AbortController();
        const res = await request(`/chat/user_input/v1`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
          body: JSON.stringify({
            mode_type: modeType,
            group_id: groupId,
            role_id: aiRoleId || roleId,
            message: message,
            conversation_id: chatInfo?.conversation_id,
            isRetry: isRetry,
            retry_message_id: msgId || '',
            language: lang,
            last_message_id: lastMsgId || getLastMsg(chatList)?.message_id
          })
        })
        if (res?.error_code === RequestStatus.OK) {
          const data = res?.data;
          console.log('human_message_id', chatList, data?.['human_message_id']);
          setChatList(draft => {
            draft[draft.length - 1].message_id = data?.['human_message_id'];
            draft[draft.length - 1].version = data?.['version'];
            draft[draft.length - 1].loadStatus = LoadStatus.Success;
          })
          resolve(res)
          // 1004 余额不足
        } else if (res?.error_code === 1004) {
          setChatList(draft => {
            draft[draft.length - 1].loadStatus = LoadStatus.Error;
            draft[draft.length - 1].errMsg = t('app.chat.no_banlance_msg');
          })
          const res = await dialogAlert.show({
            alertStatus: -1,
            desc: t('app.chat.no_banlance_msg'),
            comfirmBtn: t('app.chat.go_charge')
          })
          if (res) {
            router.push(`/${lang}/pay`);
          }
        } else {
          const errMsg = res?.message;
          setChatList(draft => {
            draft[draft.length - 1].loadStatus = LoadStatus.Error;
            draft[draft.length - 1].errMsg = errMsg;
          })
          resolve(false)
          Toast.notify({
            type: 'error',
            message: errMsg
          })
          Sentry.captureException('/chat/user_input err: ' + res?.error_code + errMsg);
        }
      } catch (error) {
        console.log('Failed to send message:', error);
        setChatList(draft => {
          draft[draft.length - 1].loadStatus = LoadStatus.Error;
        })
        resolve(false)
        Sentry.captureException(error);
      } finally {
        setResponsing(false)
      }
    })
  }
  // 用户消息，重试，AI回答
  const handleSend = async ({ message = '', isRetry = false, msgId = '', aiRoleId, lastMsgId, auto_retry = false }: any) => {
    console.log('handleSend', message);
    if (isResponsing) {
      notify({ type: 'info', message: t('app.toast.wait_data') })
      return
    }

    const placeholderAnswerItem = initAIChatTmp(aiRoleId)
    // 群组ai回答 或者 重试
    if (aiRoleId || isRetry) {
      setChatList(draft => {
        draft.push(placeholderAnswerItem)
        const msg = getLastMsg(draft);
        getReply({ message, isRetry, msgId, aiRoleId, lastMsgId: msg?.message_id, lastMsgVersion: msg?.version, auto_retry })
        updateChatList(draft)
      })
      // 群组用户发消息
    } else if (modeType === 'group') {
      sendUserChat({ aiRoleId, message, isRetry, msgId, lastMsgId })
      return;
      // 单聊用户发消息
    } else {
      const res: any = await sendUserChat({ aiRoleId, message, isRetry, msgId, lastMsgId })
      // console.log('res', res);
      if (res?.error_code === RequestStatus.OK) {
        const data = res?.data;
        setChatList(draft => {
          draft.push(placeholderAnswerItem)
          updateChatList(draft);
        })
        getReply({ message: '', isRetry, msgId, aiRoleId, lastMsgId: data?.human_message_id, lastMsgVersion: data?.version })
      }
    }
  }

  const retry = async (msgId: string, aiRoleId: number, noTips = false, auto_retry = false) => {
    const showRetryTip = localStorage.getItem('isShowRetryTip');
    if (showRetryTip != '1' && !noTips) {
      const res = await confirm.show({
        title: t('app.chat.retry_tips_title'),
        desc: t('app.chat.retry_tips_desc'),
        showCancelBtn: true,
        cancelBtn: t('app.chat.retry_tips_cancel_btn'),
        isShowIgnore: true,
        showIcon: false
      })
      if (res?.ignore) {
        localStorage.setItem('isShowRetryTip', '1')
      }
      if (!res?.confirm) {
        console.log('res', res);
        return;
      }
    }

    rollback();
    handleSend({ message: '', isRetry: true, msgId: msgId, aiRoleId, auto_retry });
  }
  // 撤回一条
  const revocation = async (msgId: string | undefined) => {
    return new Promise(async (resolve, reject) => {
      if (isResponsing) {
        cancelRespone();
        resolve(true);
      } else {
        // 如果msgId不存在，删除最后一条
        if (!msgId) {
          setChatList(draft => {
            draft.splice(-1)
          })
          resolve(true);
          return;
        }
        try {
          // let msgIds = chatList.map(chat => chat.message_id)
          // msgIds = msgIds.slice(msgIds.indexOf(msgId))
          Toast.showLoading('', true)
          const res = await request('/del_message', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              role_id: roleId,
              conversation_id: chatInfo?.conversation_id,
              message_ids: [msgId]
            })
          })
          Toast.hideLoading()
          if (res.modify_count > 0) {
            const newModelEventMap = { ...modelEventMap };
            setChatList(draft => {
              const delChatList = draft.splice(chatList.length - res.modify_count, res.modify_count)
              const lastChatMsgId = draft[draft.length - 1]?.message_id
              // 如果删除的聊天记录存在modelEvent，需要更新modelEventMap
              delChatList.forEach(chat => {
                if (chat?.message_id && newModelEventMap?.[chat.message_id] && lastChatMsgId) {
                  if (newModelEventMap?.[lastChatMsgId]) {
                    newModelEventMap[lastChatMsgId].push(...newModelEventMap[chat.message_id])
                  } else {
                    newModelEventMap[lastChatMsgId] = newModelEventMap[chat.message_id]
                  }
                }
              })
              setModelEventMap(newModelEventMap)
            })
            resolve(true);
          } else {
            throw new Error('modify_count === 0')
          }
        } catch (e) {
          Toast.hideLoading()
          Toast.notify({
            type: 'info',
            message: t('app.chat.del_failed')
          })
          resolve(false);
        }
      }
    })
  }
  const rollback = () => {
    setChatList(draft => {
      draft.splice(draft.length - 1, 1)
    })
  }
  // 
  const startNewChat = () => {
    console.log('startNewChat');
    cancelRespone();
    setChatList(() => [])
    fetchHistory(roleId, groupId, 1, chatInfo.conversation_id)
  }

  const cancelRespone = () => {
    console.log('cancelRespone');
    controller && controller.abort();
    setResponsing(false)
    rollback();
  }
  // 如果手动滚动，并且超过10px，停止自动滚动
  const handleScroll = () => {
    // const container = auth?.isAndroidTg ? document.documentElement : elementRef.current;
    const container = elementRef.current;
    if (container) {
      const { scrollTop, clientHeight, scrollHeight } = container;
      const isAutoScroll = scrollTop + clientHeight - scrollHeight > -10;
      autoScrollRef.current = isAutoScroll;
      setScrollStatus(isAutoScroll ? 'fast' : 'stop')
    }
  };
  const onblurHandler = () => {
    const container = auth?.isAndroidTg ? document.documentElement : elementRef.current;
    // ios触发键盘收起，全局滚动条回到顶部
    if (!auth?.isAndroidTg) {
      setTimeout(() => {
        window.scrollTo(0, 0)
      })
    }
  }
  const onfocusHandler = () => {
    scrollBottom('smooth');
  }

  // 获取春节活动进度
  const [springFestivalProgress, setSpringFestivalProgress] = useState<any>(null);
  const getSpringFestival = async () => {
    try {
      const res = await request(`/user/diamond_season_activity/progress`);
      if (res.error_code === 0 && res.data !== null) {
        setSpringFestivalProgress(res.data)
        // msg这个key存在，说明需要弹窗
        if (res.data.msg) {
          const dialogRes = await confirm.show({
            title: t('app.common.tips1'),
            desc: res.data.msg,
            comfirmBtn: t('app.common.confirmBtn1'),
            cancelBtn: t('app.common.cancelBtn1')
          })
          if (dialogRes?.confirm) {
            router.push(`/${lang}/pay`)
          }
        }
      }
    } catch (e) {
      console.error('获取春节活动进度失败', e);
      Sentry.captureException(e);
    }
  }
  useEffect(() => {
    getSpringFestival()
  }, [])


  const mainRef = useRef<any>(null);
  function updateVH() {
    if (window.visualViewport) {
      const container = mainRef.current;
      if (!container) return;
      // 减去头部高度
      let vh = window.visualViewport?.height - 44;
      container.style.height = `${vh}px`;
      window.scrollTo(0, 0)
    }
  }
  // tg ios聊天页面特殊兼容逻辑
  useEffect(() => {
    if (isTGIOS) {
      // 禁止ios滚动，由于高度溢出问题
      document.documentElement.classList.add('ios-chat-page');
      document.body.classList.add('ios-chat-page');
      // 监听窗口变化（如旋转屏幕、输入法弹出）时更新
      if (window.visualViewport) {
        window.visualViewport.addEventListener('resize', updateVH);
      }
    }

    return () => {
      if (isTGIOS) {
        document.documentElement.classList.remove('ios-chat-page');
        document.body.classList.remove('ios-chat-page');
        if (window.visualViewport) {
          window.visualViewport.removeEventListener('resize', updateVH);
        }
      }
    };
  }, [])

  // 非0或者非空字符串，说明是有背景图
  const isBg = user?.selected_bg_index && user?.selected_bg_index > 0 ? true : false;
  const bgUrl = user?.selected_bg_index ? [...(user?.image_bgs || []), ...(user?.official_bgs || [])]?.[user?.selected_bg_index - 1] || '/dot.png' : '/dot.png';
  const OptBgUrl = process.env.NEXT_PUBLIC_ENV === 'dev' ? bgUrl : `/_next/image?url=${encodeURIComponent(bgUrl)}&w=1200&q=90`

  return (
    <div className='pt-[42.5px]'>
      <MyNav supportMids={supportMids} setModelEventMap={setModelEventMap} chatList={chatList} modeType={modeType} conversationId={chatInfo?.conversation_id} roleId={roleId} groupId={groupId} isResponsing={isResponsing} chatInfo={chatInfo} startNewChat={startNewChat} cardName={chatInfo.card_name}></MyNav>
      {loadHistory ? <Loader msg={t('app.common.loading')} /> : <main className={cn(s.gridLayout, 'relative z-0')} ref={mainRef}><div className='pb-5 pt-2 overflow-auto text-sm leading-6 bg-no-repeat bg-cover bg-center' ref={elementRef} style={{ backgroundImage: `url(${OptBgUrl})` }}>
        <div className='mx-auto pb-16 md:w-[768px]'>
          <Intro intro={chatInfo?.introduction} scenario={chatInfo?.scenario} privateCard={chatInfo?.private_card} isBg={isBg} />
          <ExportHistory showExoprt={showExoprt} roleId={roleId} conversationId={chatInfo?.conversation_id} groudId={groupId} modeType={modeType} isBg={isBg} opacity={user?.opacity} />
          {
            chatList.map((chat, index) => {
              if (chat.type == 'human') {
                return <UserChat key={chat.message_id || index} retry={retrySendUserChat} user={user} chat={chat} isShowRevocation={chatList.length - index == 1} revocation={revocation} isBg={isBg} opacity={user?.opacity} modelEventMap={modelEventMap} modeType={modeType} />
              } else {
                return <AIChat key={chat.message_id || index} chat={chat} user={user} chatInfo={chatInfo} chatList={chatList} index={index} retry={retry} setResponsing={setResponsing} setChatList={setChatList} isShowRevocation={chatList.length - index == 1} revocation={revocation} onSend={continueHandler} isShowContinue={true} isBg={isBg} opacity={user?.opacity} modelEventMap={modelEventMap} isImgRetry={true} setChatInfo={setChatInfo} />
              }
            })
          }
          {queueInfo && !isResponsing && <Queue queueInfo={queueInfo} />}
          {/* 分享按钮 */}
          {modeType === 'single' && !web && !chatInfo?.private_card && <Share roleId={roleId} conversationId={chatInfo?.conversation_id} mode_type={modeType} chatList={chatList} isResponsing={isResponsing} />}
        </div>
      </div>
        <ChatInput
          onSend={handleSend}
          controlFocus={controlFocus}
          startNewChat={startNewChat}
          isResponsing={isResponsing}
          cancelRespone={cancelRespone}
          onBlur={onblurHandler}
          onfocus={onfocusHandler}
          conversationId={chatInfo?.conversation_id}
          chatInfo={chatInfo}
          modeType={modeType}
          groupId={groupId}
          reflesh={() => { fetchHistory(roleId, groupId, 0, chatInfo.conversation_id) }}
          // can_continue_replay={can_continue_replay}
          chatTimes={chatTimes}
          setChatTimes={setChatTimes}
          chatList={chatList}
          supportMids={supportMids} setModelEventMap={setModelEventMap}  roleId={roleId} ></ChatInput>
        {/* 春节活动 */}
        {springFestivalProgress?.task_id && !web && <div className="fixed top-[50px] right-1.5 w-30 bg-white/90 dark:bg-gray-800/90 border border-purple-500 dark:border-gray-500 rounded-md shadow-md flex items-center justify-center text-xs px-2 py-1">
          目标钻石消耗：{springFestivalProgress.diamond_consumption}/{springFestivalProgress.diamond_required}
        </div>}
      </main>}
    </div>
  )
}

const ChatPage = () => {
  return (
    <Suspense fallback={<Loader />}>
      <Chat />
    </Suspense>
  )
}

export default ChatPage
