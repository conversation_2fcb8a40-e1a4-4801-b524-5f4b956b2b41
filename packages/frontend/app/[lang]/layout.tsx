import { Crimson_Text, Delius_Swash_Caps } from "next/font/google";
import I18N from "@/app/[lang]/i18n";
import { switchLanguages } from '@little-tavern/shared/src/i18n/settings'
import { ThemeProvider } from 'next-themes'
import { AuthProvider } from "@little-tavern/shared/src/authContext";
import type { Viewport } from 'next'
import { getTranslation } from "@little-tavern/shared/src/i18n/index";
import { ConfigProvider } from "@little-tavern/shared/src/configContext";
import "./globals.css";
import './markdown.scss'
import Global from './components/global'
import {getGlobalConfig} from "@little-tavern/shared/src/module/globalConfig";
import { isTG } from "@share/src/module/global";
import { tgWeb } from "../module/global";

export async function generateMetadata({ params: { lang } }: {
  params: {
    lang: string;
  };
}) {
  const { t } = await getTranslation(lang, "app");
  return {
    title: t('meta.title'),
    description: t('meta.desc'),
  }
}

export async function generateStaticParams() {
  return switchLanguages.map(lang => ({ lang }))
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover', // Added for iOS notch adaptation
}

const crimsonText = Crimson_Text({
  weight: ['600'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-crimson-text'
});

const deliusSwashCaps = Delius_Swash_Caps({
  weight: ['400'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-delius-swash-caps'
});

export default async function RootLayout({
  children,
  params: {
    lang,
  },
}: Readonly<{
  children: React.ReactNode
  params: any
}>) {
  const config = await getGlobalConfig(lang)
  return (
    <html lang={lang} suppressHydrationWarning className={`${tgWeb && crimsonText.variable} ${tgWeb && deliusSwashCaps.variable}`}>
      {/* <body className={`${inter.className}`}> */}
      <head>
        {/* 关键代码：阻止请求 favicon.ico */}
        {isTG && <link rel="icon" href="data:;base64,iVBORw0KGgo=" />}
      </head>
      <body>
        <I18N lang={lang}>
          <ThemeProvider attribute="class" defaultTheme={'dark'}>
            <ConfigProvider _config={config || {}}>
              <AuthProvider lang={lang}>
                <Global>
                  {children}
                </Global>
              </AuthProvider>
            </ConfigProvider>
          </ThemeProvider>
        </I18N>
        {/* {process.env.NEXT_PUBLIC_ENV === 'prod' && <Analytics />} */}
      </body>
    </html>
  );
}