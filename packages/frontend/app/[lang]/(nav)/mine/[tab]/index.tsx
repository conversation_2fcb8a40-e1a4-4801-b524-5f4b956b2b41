'use client'
import React, { useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import Loader from '@little-tavern/shared/src/ui/Loading'

import Setting from './setting'
import Tabs from '@/app/[lang]/components/tabs';
import History from './history';
import { getMineTabs } from '../constants'

const Main = ({params: {lang, tab}}: any) => {
  const { t } = useTranslation()
  const auth = useContext(AuthContext);
  const userInfo = auth?.user;
  const tabs = getMineTabs(t, lang)
  
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);
  // console.log('tab', tab)
  return (
    <>
        <Tabs tabs={tabs}></Tabs>
        {!mounted ? <Loader msg={t('app.common.loading')} /> : (
          userInfo?.id == undefined ? <Loader msg={t('app.common.loading')} /> : <>
            {/* Render the appropriate component based on the active tab */}
            {(() => {
              switch (tab) {
                case 'history':
                  return <History />;
                case 'setting':
                  return <Setting />;
                default:
                  return null;
              }
            })()} 
          </>
        )}
    </>
  )
}

export default Main
