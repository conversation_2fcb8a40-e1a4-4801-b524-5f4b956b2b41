'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import Loader, { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import Tabs from '@/app/[lang]/components/tabs';
import Give from './give';
import Free from './free';
import Recharge from './recharge';
import { getMineTabs } from '../../constants'
import Fav from './fav'
import SnapShoot from './snapshoot'
import Share from './share'
import SubTabs from '../subTabs'
import { useParams, useRouter } from 'next/navigation'
import useSubTabs from './useSubTabs'
import BenifitConsume from './benifitConsume'

const Main = ({params: {lang, tab, subtab}}: any) => {
  const { t } = useTranslation()
  const auth = useContext(AuthContext);
  const userInfo = auth?.user;
  const tabs = getMineTabs(t, lang)
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const params = useParams()
  const subTabs = useSubTabs(tab)
  const selectedIndex = subTabs.findIndex((item: any) => item.key === subtab)
  useEffect(() => {
    setMounted(true);
  }, []);
  // console.log('userInfo?.id', userInfo?.id, mounted, tab, subtab);
  const clickTabHandler = (index: number) => {
    router.push(`/mine/${params.tab}/${subTabs[index].key}`)
  }
  const containerRef = useRef<HTMLDivElement>(null);
  return (
    <>
        <Tabs tabs={tabs}></Tabs>
        {!mounted ? <Loader msg={t('app.common.loading')} /> : (
          userInfo?.id == undefined ? <Loader msg={t('app.common.loading')} /> :  <div className='overflow-y-auto' id='scrollableDiv' ref={containerRef}>
            <SubTabs tabs={subTabs} clickTabHandler={clickTabHandler} selectedIndex={selectedIndex} />
            {
              tab === tabs[0].key && subtab === 'recharge' && <><Recharge></Recharge></>
            }
            {
              tab === tabs[0].key && subtab === 'give' && <><Give></Give></>
            }
            {
              tab === tabs[0].key && subtab === 'free' && <><Free></Free></>
            }
            {
              tab === tabs[0].key && subtab === 'benifit-consume' && <><BenifitConsume containerRef={containerRef}></BenifitConsume></>
            }
            {
              tab === tabs[2].key && subtab === 'share' && <Share />
            }
            {
              tab === tabs[2].key && subtab === 'fav' && <><Fav></Fav></>
            }
            {
              tab === tabs[2].key && subtab === 'snapshoot' && <><SnapShoot></SnapShoot></>
            }
          </div>
        )}
    </>
  )
}

export default Main
