'use client'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, useRouter } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'

const Give = () => {
    const { t } = useTranslation()
    const params = useParams()
    const auth = useContext(AuthContext);
    const user = auth?.user;
    const request = useRequest();
    const [benefitList, setBenefitList] = useState<any>([])
    const [isLoad, setIsLoad] = useState<boolean>(false)

    useEffect(() => {
        getChatBenefits()
        setIsLoad(true)
    }, []);
    const getChatBenefits = async () => {
        const res = await request(`/user/chat/benefit?user_benefit_category=RECHARGE`)
        console.log('res', res);
        setBenefitList(res?.benefit_list || [])
        setIsLoad(false)
    }
    return (
        <div className='mx-2 px-3 py-3 bg-white dark:bg-gray-900 rounded text-sm text-gray-600 dark:text-gray-300 overflow-y-auto mb-2'>

            <div className="px-2 py-1 sm:w-[500px] flex flex-wrap gap-2 justify-center sm:justify-normal">
                {benefitList.length > 0 && benefitList.map((item: any, index: number) => {
                    return <div key={index} className="rounded-md w-full p-3 border dark:border-gray-500 dark:bg-gray-900 bg-white">
                        <div className=''>
                            <div className='mb-2'>
                                {item.benefit_title}
                            </div>
                            <div>
                                <ul>
                                    <li>{t('app.mine.chat_mode')}：{item.model_display_name}</li>
                                    <li>{t('app.mine.quota')}：{item.remain_times}/{item.reward_times}(次)</li>
                                    <li>{t('app.mine.validity_period')}：{item.expire_at}</li>
                                    {item.next_receive_at && <li>{t('app.mine.next_receive_at')}：{item.next_receive_at}</li>}
                                </ul>
                            </div>
                        </div>
                    </div>
                })}
                {!isLoad && benefitList.length == 0 && <div className='text-center text-gray-500 text-sm'>{t('app.mine.no_benefit')}</div>}
            </div>
        </div>
    )
}

export default Give