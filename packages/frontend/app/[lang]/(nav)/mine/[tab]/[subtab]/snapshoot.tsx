'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import useSWRImmutable, { mutate } from 'swr';
import Card from '@little-tavern/shared/src/ui/card'
import { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import NoContent from '@little-tavern/shared/src/ui/noContent'
import InfiniteScroll from 'react-infinite-scroll-component';

const limit = 10;
const url = `/user/chat/snapshot/list`;
const Fav = () => {
    const { t } = useTranslation()
    const auth = useContext(AuthContext);
    const userInfo = auth?.user;
    const request = useRequest();
    const [offset, setOffset] = useState(0);
    const { data, isLoading, error, mutate } = useSWRImmutable(userInfo?.id ? `${url}?offset=${offset}&limit=${limit}` : null, request, {
        revalidateOnFocus: false
    });
    const [list, setList] = useState<any>(data?.data?.user_snapshots || [])
    const fetchSnashootList = useCallback(async () => {
        setOffset(0)
        mutate(url);
    }, [])

    useEffect(() => {
        console.log('data', data)
        if (data?.data?.user_snapshots?.length >= 0) {
            setList([...(offset === 0 ? [] : list), ...data.data.user_snapshots])
        }
    }, [data]);

    let hasMore = true;
    if (data?.data?.total) {
        hasMore = offset < data.data.total - limit;
    }
    const loadMore = () => {
        if (!hasMore || isLoading) return;
        !error && setOffset((offset + limit))
    };
    const retry = () => {
        mutate()
    }
    return (
        <InfiniteScroll
            dataLength={list.length}
            next={loadMore}
            hasMore={hasMore}
            loader={list.length > 0 ? <div className="text-center text-xs mb-3">{t('app.index.loading')}...</div> : null}
            endMessage={
                !hasMore && !isLoading ? (
                    <div className="text-center text-xs mb-3">{t('app.index.no_card_load')}</div>
                ) : null
            }
            scrollableTarget="scrollableDiv"
        >
            <div className="px-2 pt-2 pb-2 grid grid-cols-1 sm:grid-cols-2 gap-2 lg:gap-3 lg:grid-cols-4 xl:grid-cols-5">
                {
                    list.length > 0 ?
                        list.map((role: any) => {
                            return <Card key={role.id} modeType={role.card_detail.mode_type} reflesh={fetchSnashootList} role={{ ...role.card_detail.role, card_name: role.title, isShowTags: true, showSnapshoot: true, user_snapshot_id: role.id, direct: true }}></Card>
                        }) :
                        <>{userInfo?.id !== undefined && list?.length == 0 && !isLoading && <NoContent />}</>
                }
            </div>
            {userInfo?.id === undefined || isLoading && offset == 0 && <LoadingToast msg={t('app.common.loading')} />}
            {error && !isLoading && <LoadingToastFailed retry={retry} error={error} />}
        </InfiniteScroll>
    )
}

export default Fav