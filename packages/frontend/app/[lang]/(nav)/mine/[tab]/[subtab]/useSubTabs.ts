'use client'

import { useTranslation } from 'react-i18next'

export interface TabItem {
  title: string
  key: string
}

const useSubTabs = (key: string) => {
  const { t } = useTranslation()
  const tabs: any = {
    gift: [
      {
        title: t('app.mine.benefits_free'),
        key: 'free'
      }, {
        title: t('app.mine.benefits_recharge'),
        key: 'recharge'
      }, {
        title: t('app.mine.benefits_give'),
        key: 'give'
      },
      {
        title: t('app.mine.benifit_consume'),
        key: 'benifit-consume'
      }
    ],
    card: [
      {
        title: t('app.mine.snapshoot'),
        key: 'snapshoot'
      },
      {
        title: t('app.mine.my_share'),
        key: 'share'
      }, {
        title: t('app.mine.fav'),
        key: 'fav'
      },
      
    ]
  }
  return tabs[key]
}
export default useSubTabs