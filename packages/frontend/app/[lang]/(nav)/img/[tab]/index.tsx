'use client'
import React, { useCallback, useContext, useEffect, useLayoutEffect, useRef, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { AuthContext } from '@little-tavern/shared/src/authContext'
import Illustrate from '@share/src/ui/tooltip/illustrate'
import Tabs from '@/app/[lang]/components/tabs'
import { useTranslation } from 'react-i18next'
import Gen from './Gen'
import Mine from './Mine'


const Index = ({ params: { lang, tab } }: any) => {
  const { t } = useTranslation()
  const auth = useContext(AuthContext);
  // console.log('data', data);
  const reouter = useRouter();
  const tabs = [
    {
      key: 'gen',
      title: t('app.img.create'),
      link: `/${lang}/img/gen`
    },
    {
      key: 'mine',
      title: t('app.img.mine'),
      link: `/${lang}/img/mine`
    }
  ]
  
  return (
    <>
      <main className="main-height w-full con-width">
        <div className='flex items-center justify-between'>
          <Tabs className='inline-block !text-base' tabs={tabs}></Tabs>
          <div className='mr-2 text-sm text-gray-500'>{t('app.img.title')} <Illustrate title={t('app.img.illustrate')} desc={t('app.img.illustrate_desc')} /></div>
        </div>
        {
          tab === tabs[0].key && <Gen />
        }
        {
          tab === tabs[1].key && <Mine />
        }
      </main>
    </>
  )
}

export default React.memo(Index)
