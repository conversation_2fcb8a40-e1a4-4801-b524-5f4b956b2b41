'use client'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import useRequest from '@share/src/hook/useRequest'
import cn from 'classnames'
import Toast from '@share/src/ui/toast'
import Image from 'next/image'
import useSWR  from 'swr';
import InfiniteScroll from 'react-infinite-scroll-component';
import s from '@/app/[lang]/globals.module.css'
import Tags from './tags'
import format from '@/app/module/formate-date'
import { Trash } from 'lucide-react';
import Link from 'next/link'
import useComfirm from '@share/src/hook/useComfirm'
import Loader from '@little-tavern/shared/src/ui/Loading'
import NoContent from '@share/src/ui/noContent'
import useLightBox from '@share/src/hook/useLightBox'
import sdk from '@share/src/module/sdk'
import Download from './download'


const limit = 20;
const Mine = () => {
    const { t } = useTranslation()
    const [list, setList] = useState<any[]>([])
    const request = useRequest()
    const [offset, setOffset] = useState(0)
    const comfirm = useComfirm()
    const lightBox = useLightBox();

    const { data, isLoading, error, mutate } = useSWR(`/image/generate/user/records?offset=${offset}&limit=${limit}`, request)

    useEffect(() => {
        if(offset === 0 && data?.data?.records?.length > 0) {
            setList(data.data.records)
        } else if(data?.data?.records?.length > 0) {
            setList([...list, ...data.data.records])
        }
    }, [data])
    let hasMore = true;
    const count = data?.data?.count || 0;
    if (count) {
        hasMore = offset < count - limit;
    }
    const loadMore = () => {
        if (!hasMore || isLoading) return;
        setOffset(offset + limit)
    };
    const deleteHandler = async (id: string) => {
        const comfirmRes = await comfirm.show({
            title: t('app.img.delete'),
            desc: t('app.img.delete_desc')
        })
        if(comfirmRes.confirm) {
            try {
                Toast.showLoading('')
                const res = await request(`/image/generate/user/record/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ record_id: id })
                })
                Toast.hideLoading()
                if(res.error_code === 0) {
                    Toast.notify({
                        type: 'success',
                        message: t('app.img.delete_success')
                    })
                    // 更新本地列表数据
                    setList((list: any) => {
                        return list.filter((item: any) => item.id !== id)
                    })
                } else {
                    Toast.notify({
                        type: 'error',
                        message: res.message
                    })
                }
            } catch (error) {
                Toast.hideLoading()
                Toast.notify({
                    type: 'error',
                    message: t('app.img.delete_failed')
                })
            }
        }
    }
    return (
        <div id="scrollableDiv" className='h-[calc(100vh_-_94px_-_var(--bottom-margin))] overflow-y-auto'>
            {<InfiniteScroll
                dataLength={list.length}
                next={loadMore}
                hasMore={hasMore}
                loader={list.length > 0 ? <div className="text-center text-xs mb-3">{t('app.index.loading')}...</div> : isLoading ? <Loader /> : <div className='col-start-1 sm:col-span-2 lg:col-span-4 xl:col-span-5 flex justify-center w-full'><NoContent /></div>}
                endMessage={
                    !hasMore && !isLoading && list.length > 4 ? (
                        <div className="text-center text-xs mb-3">{t('app.index.no_card_load')}</div>
                    ) : null
                }
                scrollableTarget="scrollableDiv"
            >
                <div className="px-2 pt-2 pb-2 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-3 lg:grid-cols-4 xl:grid-cols-5">
                    {list.map((item: any) => (
                        <div key={item.id} className={cn('relative overflow-hidden cursor-pointer w-full max-w-full rounded-md py-2 dark:border dark:border-gray-500 dark:bg-gray-900 bg-white')}>
                            <div className={cn("relative w-[70px] h-[105px] overflow-hidden rounded dark:bg-gray-800 bg-gray-300 ml-2 mr-3 float-left flex items-center justify-center")}>
                                {item.status === 'SUCCESS' && <Image unoptimized={true} src={item.image_url} alt="" width={item.image_width} height={item.image_height} className='w-full h-full object-cover' onClick={(e) => {
                                    lightBox.show({src: item.image_url, unoptimized: true});
                                }} />}
                                {item.status === 'FAILED' && <div className='w-full h-full bg-gray-300 dark:bg-gray-800 flex items-center justify-center text-sm text-gray-400'>{item.error_message}</div>}
                                {(item.status === 'GENERATING' || item.status === 'SUBMIT') && <div className='w-full h-full bg-gray-300 dark:bg-gray-800 flex items-center justify-center text-sm text-gray-400'>{t('app.img.generating')}</div>}
                                <div className='absolute left-0 text-center right-0 bottom-0.5 px-2 bg-gray-800/50 text-xs'>{item.image_width}x{item.image_height}</div>
                            </div>
                            <div className={cn("relative mr-2 overflow-hidden h-[105px] flex flex-col justify-between")}>
                                <div className={cn("line-clamp-2 hover:underline desc text-sm dark:text-gray-200 text-gray-600 h-22 pt-0.5 xl:line-clamp-1")}>{item.prompt}</div>
                                <div>
                                    <Tags className={cn('max-h-10 overflow-hidden mr-5')} tags={item.display_tags} />
                                    <div className='flex justify-between'>
                                        <div className='text-sm text-gray-400 flex items-center'>{format(item.generate_time * 1000, 'YYYY/MM/DD')}</div>
                                        <div className='flex gap-0.5 text-xs'>
                                            <Download url={item.image_url} />
                                            <button className='p-1' type='button' onClick={() => deleteHandler(item.id)}><Trash className='w-4 h-4' /></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </InfiniteScroll>}
        </div>
    )
}

export default Mine
