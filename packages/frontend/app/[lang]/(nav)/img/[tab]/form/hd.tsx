
import { useTranslation } from 'react-i18next'
import { useConfig } from '@share/src/configContext'

const Hd = ({ register, errors }: any) => {
  const { t } = useTranslation()
  const config = useConfig()
  return <>
    <div className="mb-3">
      <label className="text-sm font-medium leading-6 mr-1 align-middle text-gray-400">
        {t('app.img.hd')} {errors.image_quality && (
            <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.image_quality.message}</span>
          )}
      </label>
      <div className='mt-0.5'>
        {config?.image_quality_list?.map((item: any) => {
          return <label key={item.key} className='mr-2 align-middle'><input className='mr-0.5' {...register("image_quality", { required: {
            value: true, message: t('app.img.require')
          } })} type="radio" value={item.key} />{item.name}</label>
        })}
      </div>
    </div>
  </>
}

export default Hd