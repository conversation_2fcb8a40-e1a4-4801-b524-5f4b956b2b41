import { useConfig } from "@share/src/configContext"
import { useTranslation } from "react-i18next"
const Style = ({ errors, register }: any) => {
  const { t } = useTranslation()
  const config = useConfig()
  return <>
    <div className="mb-3">
      <div className='flex items-center'>
        <label className="block text-sm font-medium leading-6 text-gray-400 ">
          {t('app.img.custom_style')} {errors.image_style && (
            <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.image_style.message}</span>
          )}
        </label>
      </div>
      <div className="mt-0.5">
        <div className='flex gap-2 gap-y-1 flex-wrap'>
          {config?.image_style_list?.map((item: any) => {
            return <label key={item.key} className='mr-2 align-middle'><input className='mr-0.5' {...register("image_style", { required: {
              value: true, message: t('app.img.require')
            } })} type="radio" value={item.key} />{item.name}</label>
          })}
        </div>
      </div>
    </div>
  </>
}
export default Style