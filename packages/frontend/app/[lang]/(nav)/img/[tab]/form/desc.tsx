import AutoTextareaHeight from "@share/src/ui/dialog/CreateCard/AutoTextareaHeight"
import { useTranslation } from "react-i18next"

const Desc = ({ register, formValues, errors }: any) => {
    const { t } = useTranslation()
    return (
        <div className="mb-3">
            <div className='flex items-center'>
                <label className="block text-sm font-medium leading-6 text-gray-400 -mb-1">
                    {t('app.img.create_desc')} {errors.image_quality && (
                        <span className='text-xs mt-1 text-white bg-red-700 px-1 rounded inline-block py-0.5'>{errors.image_quality.message}</span>
                    )}
                </label>
            </div>
            <AutoTextareaHeight {...register('prompt', {
                required: {
                    value: true, message: t('app.img.require')
                }
            })} required value={formValues.prompt} rows={4} minHeight={'min-h-36'} className='dark:bg-gray-800 dark:ring-gray-500' placeholder={t('app.img.create_desc_placeholder')}></AutoTextareaHeight>
        </div>
    )
}

export default Desc