import {isTG} from '@share/src/module/global'
import Link from 'next/link'
import sdk from '@share/src/module/sdk'
import { Download as DownloadIcon } from 'lucide-react';
import cn from 'classnames'
const Download = ({url, className = ''}: {url: string, className?: string}) => {
    return <>
        {url && isTG && <button type="button" onClick={() => {
            sdk.downloadFile({
                url,
                file_name: url.split('/').pop()
            })
        }} className={cn('p-1', className)}><DownloadIcon className='w-4 h-4' /></button>}
        {url && !isTG && <Link target='_blank' href={url} className={cn('p-1', className)}><DownloadIcon className='w-4 h-4' /></Link>}
    </>
}

export default Download