import useLocalStorage from "@share/src/hook/useLocalStorage";
import React, { useContext, useEffect } from 'react';
import { AuthContext } from '@little-tavern/shared/src/authContext';
import useRequest from "@share/src/hook/useRequest";

/**
 * 缓存首次请求的数据
 */
// 小程序生命周期期间，只能缓存一次
let hasCached = false;
const useCacheHook = ({nsfw, pageData}: {nsfw: boolean, pageData: any}) => {
    const [firstPageData, setFirstPageData] = useLocalStorage('firstPageData', '{}')
    const auth = useContext(AuthContext);
    const user = auth?.user
    const request = useRequest();

    const cacheFirstPageData = async (tag: string) => {
        const res = await request(`/roles/user_filter_list?nsfw=${nsfw}${tag ? `&tag=${tag}` : ''}&offset=0&limit=20${tag ? `&sort_type=${tag}` : ''}`)
        if (res?.card_list.length > 0) {
            setFirstPageData({
                card_list: res?.card_list,
                tags: pageData.tags,
                count: res?.count,
                currentTag: tag,
            })
        }
    }
    useEffect(() =>{
        if (!user?.display_summary_rank_tag || hasCached) return;
        cacheFirstPageData(user?.display_summary_rank_tag)
        hasCached = true;
    }, [user?.display_summary_rank_tag])
    return [firstPageData]
}
export default useCacheHook
