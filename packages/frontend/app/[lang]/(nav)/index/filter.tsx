import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import cn from 'classnames'
import Modal from '@share/src/ui/dialog/Modal';
import { CheckCheckIcon, ListFilterIcon } from 'lucide-react';
import { ChevronDownIcon } from '@heroicons/react/20/solid'

const btnClass = 'flex relative items-center rounded-full border border-purple-400 dark:border-gray-300 px-3 mr-1.5 mb-1.5 py-1 text-xs dark:text-white';
const selectClass = 'text-purple-400 ml-0.5 inline-block w-3 h-3'
const Filter = ({ pageData, onFilter }: any) => {
    const config = pageData?.config
    const currentSubtag = pageData?.currentSubtag
    const { t } = useTranslation('app', { keyPrefix: 'app.index.filter' });
    const [showSubtags, setShowSubtags] = useState(false)
    const subTagsCatalog = config?.sub_tag_categories || []
    const selectedSubtags = currentSubtag && currentSubtag?.split(',').map((tag: string) => tag.trim()) || [];
    const [activeSubtag, setActiveSubtag] = useState(selectedSubtags);
    const tags: any = {}

    // 聊天模式
    const chatModels = config?.chat_models || []
    const selectedChatMode = pageData?.chat_model_id
    
    // 性别
    const roleGenders = config?.role_genders || []
    const selectedRoleGender = pageData?.role_gender

    // 聊天类型，独立角色
    const chatModeOptions = config?.chat_types || []
    const selectedChatType = pageData?.chat_type

    // 抢话筛选
    const playTypeOptions = config?.play_types || []
    const selectedPlayType = pageData?.play_type

    // 排序
    const sortOptions = config?.sort_types || []
    const selectedSort = pageData?.sort_type

    // console.log('selectedChatMode', chatModeOptions, selectedChatMode);
    // 把sub_tags里面的category作为tags的key，value为sub_tags
    config?.sub_tags?.forEach((subTag: any) => {
        const key = subTag.category;
        if (!tags[key]) {
            tags[key] = [];
        }
        tags[key].push(subTag);
    })

    // 切换标签
    const handlerSwitchSubtags = (subTag: any) => {
        const newActiveSubtag = activeSubtag.includes(subTag) ? activeSubtag.filter((tag: any) => tag !== subTag) : [...activeSubtag, subTag];
        setActiveSubtag(newActiveSubtag);
    }
    // 确定标签
    const confirmTagSelect = () => {
        setShowSubtags(false);
        onFilter({
            subtag: activeSubtag.join(',')
        });
    }

    // 切换聊天模式
    const handlerSwitchChatMode = (chatModelId: string) => {
        onFilter({
            chat_model_id: chatModelId,
        });
    }

    // 切换聊天类型，独立角色
    const handlerSwitchChatType = (chatType: any) => {
        onFilter({
            chat_type: chatType
        });
    }

    // 切换抢话筛选
    const handlerSwitchPlayType = (playType: any) => {
        onFilter({
            play_type: playType
        });
    }

    // 切换排序
    const handlerSwitchSort = (sort: any) => {
        onFilter({
            sort_type: sort
        });
    }
    const showTabModal = () => {
        setActiveSubtag(selectedSubtags);
        setShowSubtags(true)
    }
    return <div className='px-2'>
        <div className='flex items-center flex-wrap'>
            {/* 只有日周月榜单选中才出现 */}
            {(pageData.currentTag === 'MONTHLY' || pageData.currentTag === 'WEEKLY' || pageData.currentTag === 'DAILY') && <div className={`${btnClass} ${selectedSort && '!border-purple-400 border-2'}`}>
                {selectedSort ? <div className=''>{sortOptions.find((item: any) => item.key === selectedSort)?.name}</div> : sortOptions.find((item: any) => item.key === pageData.currentTag)?.name}
                <ChevronDownIcon className={cn("-mr-1 h-3.5 w-3.5 text-violet-500  dark:text-zinc-100")} />
                <select
                    className='text-center bg-white dark:bg-black dark:text-white w-full h-full absolute opacity-0 left-0 top-0'
                    value={selectedSort || ''}
                    onChange={(e) => handlerSwitchSort(e.target.value)}
                >
                    {
                        sortOptions.map((item: any) => {
                            return <option key={item.key} value={item.key}>{item.name}</option>
                        })
                    }
                </select>
            </div>}
            {/* 在精选、热门、上新、榜单选择后出现 */}
            {(pageData.currentTag === 'CHOSEN' || pageData.currentTag === 'HOT' || pageData.currentTag === 'NEW' || pageData.currentTag === 'MONTHLY' || pageData.currentTag === 'WEEKLY' || pageData.currentTag === 'DAILY') && <div className={`${btnClass} ${selectedRoleGender && '!border-purple-400'}`}>
                {selectedRoleGender ? <span className='truncate w-fit'>{roleGenders.find((item: any) => item.key === selectedRoleGender)?.name}</span> : t('gender')}
                <ChevronDownIcon className={cn("-mr-1 h-3.5 w-3.5 text-violet-500  dark:text-zinc-100")} />
                <select
                    className='text-center bg-white dark:bg-black dark:text-white w-full h-full absolute opacity-0 left-0 top-0'
                    value={selectedRoleGender || ''}
                    onChange={(e) => {
                        onFilter({
                            role_gender: e.target.value,
                        });
                    }}
                >
                    <option value="">{t('default')}</option>
                    {
                        roleGenders.map((item: any) => {
                            return <option key={item.key} value={item.key}>{item.name}</option>
                        })
                    }
                </select>
            </div>}
            <button className={`${btnClass} ${activeSubtag.length > 0 && '!border-purple-400'}`} onClick={showTabModal}>
                {t('tag')}
                {activeSubtag.length > 0 ? <CheckCheckIcon className={selectClass} /> : <ListFilterIcon className="ml-0.5 inline-block w-3 h-3 text-violet-500  dark:text-zinc-100" />}
            </button>
            <div className={`${btnClass} ${selectedChatMode && '!border-purple-400'}`}>
                {selectedChatMode ? <span className='truncate w-12'>{chatModels.find((item: any) => item.key === selectedChatMode)?.name}</span> : t('chat_mode')}
                <ChevronDownIcon className={cn("-mr-1 h-3.5 w-3.5 text-violet-500  dark:text-zinc-100")} />
                <select
                    className='text-center bg-white dark:bg-black dark:text-white w-full h-full absolute opacity-0 left-0 top-0'
                    value={selectedChatMode || ''}
                    onChange={(e) => handlerSwitchChatMode(e.target.value)}
                >
                    <option value="">{t('all_model')}</option>
                    {
                        chatModels.map((item: any) => {
                            return <option key={item.key} value={item.key}>{item.name}</option>
                        })
                    }
                </select>
            </div>
            {pageData.currentTag !== 'GROUP' && <div className={`${btnClass} ${selectedChatType && '!border-purple-400'}`}>
                {selectedChatType ? <div className='truncate w-12'>{chatModeOptions.find((item: any) => item.key === selectedChatType)?.name}</div> : t('independent_role')}
                <ChevronDownIcon className={cn("-mr-1 h-3.5 w-3.5 text-violet-500  dark:text-zinc-100")} />
                <select
                    className='text-center bg-white dark:bg-black dark:text-white w-full h-full absolute opacity-0 left-0 top-0'
                    value={selectedChatType || ''}
                    onChange={(e) => handlerSwitchChatType(e.target.value)}
                >
                    <option value="">{t('default')}</option>
                    {
                        chatModeOptions.map((item: any) => {
                            return <option key={item.key} value={item.key}>{item.name}</option>
                        })
                    }
                </select>
            </div>}
            {pageData.currentTag !== 'GROUP' && <div className={`${btnClass} ${selectedPlayType && '!border-purple-400'}`}>
                {selectedPlayType ? <div className='truncate w-12'>{playTypeOptions.find((item: any) => item.key === selectedPlayType)?.name}</div> : t('speech_filter')}
                <ChevronDownIcon className={cn("-mr-1 h-3.5 w-3.5 text-violet-500  dark:text-zinc-100")} />
                <select
                    className='text-center bg-white dark:bg-black dark:text-white w-full h-full absolute opacity-0 left-0 top-0'
                    value={selectedPlayType || ''}
                    onChange={(e) => handlerSwitchPlayType(e.target.value)}
                >
                    <option value="">{t('default')}</option>
                    {
                        playTypeOptions.map((item: any) => {
                            return <option key={item.key} value={item.key}>{item.name}</option>
                        })
                    }
                </select>
            </div>}
        </div>

        <Modal isOpen={showSubtags} className={'!top-[47%]'} onClose={() => { setShowSubtags(false) }} >
            <div className='w-full px-1 max-h-[calc(100vh_-_250px)] overflow-auto'>
                <div className='text-sm mt-3 mb-1 flex items-center'>
                    <span className='mr-1'>{t('select_tag')}</span>
                    {activeSubtag?.length > 0 && <div className='flex px-2 py-1.5 rounded bg-white dark:bg-black/40 dark:text-white text-gray-600 max-w-[70%]'>
                        {
                            <div className='text-xs truncate'>
                                {config?.sub_tags?.filter((item: any) => activeSubtag.includes(item.key)).map((item: any) => item.name).join(', ')}
                            </div>
                        }
                        <button className='text-xs text-orange-500 ml-2 whitespace-nowrap' onClick={() => { setActiveSubtag([]) }}>{t('clear')}</button>
                    </div>}
                </div>
                {subTagsCatalog?.map((item: any) => {
                    return <div key={item.name}>
                        <h2 className='text-sm mt-2 mb-1'>
                            {item.name}
                        </h2>
                        {tags[item.name]?.map((subTag: any, index: number) => {
                            return <button
                                key={index}
                                className={`text-purple-500 border-solid border border-purple-500 dark:border-0 px-2 mr-1.5 mb-1.5 py-1 rounded-lg text-xs dark:text-gray-100 dark:bg-gray-700 bg-white ${activeSubtag.includes(subTag.key) ? '!bg-purple-500 !text-white' : ''}`}
                                onClick={() => handlerSwitchSubtags(subTag.key)}>
                                {subTag.name}
                            </button>
                        })}
                    </div>
                })}
            </div>
            <button className='w-full rounded-full bg-purple-500 px-3 py-2 mt-2 text-sm text-white' onClick={confirmTagSelect}>{t('common.comfirm', { keyPrefix: 'app' })}</button>
        </Modal>
        <div className='pl-1 pt-0.5 flex flex-wrap gap-2 text-xs text-pink-400' onClick={showTabModal}>
            <span className='underline'>#{t('mother_son')}</span>
            <span className='underline'>#{t('contrast')}</span>
            <span className='underline'>#{t('ntr')}</span>
            <span className='underline'>#{t('licentious')}</span>
            <span className='underline'>#{t('virgin')}</span>
            <span className='underline'>#{t('incest')}</span>
            <span className='underline'>#{t('fanfiction')}</span>
        </div>
    </div>;
};

export default memo(Filter);