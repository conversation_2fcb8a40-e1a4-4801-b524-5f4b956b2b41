"use client";
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import useRequest from '@little-tavern/shared/src/hook/useRequest'
import Loader, { LoadingToast, LoadingToastFailed } from '@little-tavern/shared/src/ui/Loading'
import TabContent from './TabContent'
import useSWRImmutable from 'swr';
import { Switch } from '@headlessui/react'
import { useTranslation } from 'react-i18next'
import debounce from 'lodash.debounce';
import Search from './search'
import { AuthContext } from '@little-tavern/shared/src/authContext';
import usePagesState from '@little-tavern/shared/src/hook/usePageState'
import useComfirm from '@share/src/hook/useComfirm';
import Image from 'next/image';
import useLocalStorage from '@little-tavern/shared/src/hook/useLocalStorage';
import { useConfig } from '@share/src/configContext';
import updateSetting from '@share/src/module/updateSetting';
import Filter from './filter';
import { limit } from './common';
import InfiniteScroll from 'react-infinite-scroll-component';
import Tabs from './Tabs'
import useCacheHook from './useCacheHook';

const VIST_NEW_TAB_TIME = 'index_visit_new_tab_time'
// 是否从config或者缓存读取数据
let readFreomCache = false;
interface TabSwitcherProps {
    nsfwData?: {
        card_list: any[];
        count: number;
    };
    normalData?: {
        card_list: any[];
        count: number;
    };
}
const TabSwitcher = ({ nsfwData, normalData }: TabSwitcherProps) => {
    const { t } = useTranslation()
    const [visitNewTime, setVisitNewTime] = useState(99999999999)
    const request = useRequest();
    const auth = useContext(AuthContext);
    const user = auth?.user
    const config = useConfig();
    const isLogin = auth?.isLogin;
    // console.log('config', config);
    // 如果不是指定的sfwApp，默认为true
    const [enabledNSFW, setEnabledNSFW] = useLocalStorage('enable_nsfw', !config?.sfwApp)
    // 默认为true
    const nsfw = enabledNSFW === null ? true : enabledNSFW
    // console.log('enabledNSFW', enabledNSFW, config?.sfwApp);
    // 根据用户设置选择初始数据
    const initialData = nsfw ? nsfwData : normalData
    
    // 生成标签
    const genTags = (key?: string) => {
        let tags = []
        const summary_rank_tags = config?.summary_rank_tags;
        const index = summary_rank_tags?.findIndex((item: any) => item.key === key)
        if(index !== -1 && index !== undefined) {
            tags = [summary_rank_tags?.[index], ...(config?.role_tags || [])]
        } else if(summary_rank_tags?.length !== undefined && summary_rank_tags?.length > 0) {
            tags = [summary_rank_tags?.[0], ...(config?.role_tags || [])]
        } else {
            tags = config?.role_tags || []
        }
        return tags
    }
    
    const [pageData, updatePageData] = usePagesState('index', {
        roles: [],
        offset: 0,
        tags: genTags(),
        currentTag: 'CHOSEN',
        currentSubtag: '',
        count: initialData?.count || 99999999,
        fetchedOffsets: [],
        scrollPosition: 0,
        chat_model_id: '',
        chat_type: '',
        play_type: '',
        sort_type: '',
        role_gender: '',
        newTabCreateTime: config?.latest_card_created_at || Date.now() / 1000,
        config: config
    })
    // console.log('pageData roles', pageData)
    const [firstPageData] = useCacheHook({nsfw, pageData})
    // 如果服务端的config请求异常，使用本地的config
    useEffect(() => {
        // 只有pageData.tags.length === 0时，才更新
        if(pageData.tags.length === 0 && user?.display_summary_rank_tag) {
            updatePageData((draft: any) => {
                draft.config = config
                draft.tags = config?.role_tags || []
                draft.newTabCreateTime = config?.latest_card_created_at || Date.now() / 1000
                const summaryRankTags = config?.summary_rank_tags || []
                const tag = summaryRankTags.find((item: any) => item.key === user?.display_summary_rank_tag)
                tag && updatePageData((draft: any) => {
                    draft.tags = [tag, ...draft.tags]
                })
            })
        }
    }, [config, user?.display_summary_rank_tag])
    const offset = pageData.offset;
    const currentTag = pageData.currentTag;
    const currentSubtag = pageData.currentSubtag;
    const canFetch = readFreomCache && !pageData.fetchedOffsets.includes(`${nsfw}-${currentTag}-${currentSubtag}-${pageData.chat_model_id}-${pageData.chat_type}-${pageData.play_type}-${pageData.sort_type}-${offset}`);
    // console.log('canFetch', canFetch);

    const { data, isLoading, error, mutate } = useSWRImmutable(
        canFetch ? `/roles/user_filter_list?nsfw=${nsfw}${currentTag ? `&tag=${currentTag}` : ''}${currentSubtag ? `&sub_tag_ids=${currentSubtag}` : ''}&offset=${offset}&limit=${limit}${pageData.chat_model_id ? `&chat_model_id=${pageData.chat_model_id}` : ''}${pageData.chat_type ? `&chat_type=${pageData.chat_type}` : ''}${pageData.play_type ? `&play_type=${pageData.play_type}` : ''}${pageData.sort_type ? `&sort_type=${pageData.sort_type}` : ''}${pageData.role_gender ? `&gender_type=${pageData.role_gender}` : ''}` : null,
        request,
        {
            revalidateOnFocus: false
        }
    );
    // console.log('data', data);
    let hasMore = true;
    if (pageData.count) {
        hasMore = offset < pageData.count - limit;
    }
    const loadMore = () => {
        // console.log(!hasMore, isLoading);
        if (!hasMore || isLoading) return;
        updatePageData((draft: any) => {
            // console.log(draft.offset +  limit);
            !error && (draft.offset = pageData.roles.length)
        })
    };
    const containerRef = useRef<HTMLDivElement>(null);
    // 移除手动滚动监听和 handleScroll 相关逻辑
    // 滚动加载交由 InfiniteScroll 组件处理

    // 恢复滚动位置
    useEffect(() => {
        if (containerRef.current && pageData.scrollPosition) {
            containerRef.current?.scrollTo({
                top: pageData.scrollPosition,
                behavior: 'auto' // 使用'auto'而不是'smooth'以避免视觉跳动
            });
        }
    }, [containerRef]);

    useEffect(() => {
        // console.log('data', pageData, data);
        if (data?.card_list.length > 0) {
            updatePageData((draft: any) => {
                draft.roles = [...(pageData.offset === 0 ? [] : pageData.roles), ...data.card_list]
                draft.sub_tags = data.sub_tags
                draft.count = data.count
                draft.fetchedOffsets.push(`${nsfw}-${currentTag}-${currentSubtag}-${pageData.chat_model_id}-${pageData.chat_type}-${pageData.play_type}-${pageData.sort_type}-${offset}`)
            })
        } else if (data?.card_list.length === 0 && offset === 0) {
            updatePageData((draft: any) => {
                draft.roles = []
                draft.count = 0
                draft.fetchedOffsets.push(`${nsfw}-${currentTag}-${currentSubtag}-${pageData.chat_model_id}-${pageData.chat_type}-${pageData.play_type}-${pageData.sort_type}-${offset}`)
            })
        }
    }, [data]);

    // 当 NSFW 设置改变时，且不带标签的默认首页的时候，更新数据
    const resetWithNSFWAwitch = (nsfw: boolean) => {
        if (currentTag) return;
        const newData = nsfw ? nsfwData : normalData
        if (newData) {
            updatePageData((draft: any) => {
                draft.roles = newData.card_list
                draft.count = newData.count
                draft.offset = 0
                draft.fetchedOffsets = []
            })
        }
    }
    // 首次加载，当 NSFW从null变为true或者false的时候，且从缓存读取数据时，更新数据
    const prevEnabledNSFWRef = useRef(enabledNSFW);
    useEffect(() => {
        if (prevEnabledNSFWRef.current === null && enabledNSFW !== null && firstPageData !== null) {
            // console.log('firstPageData', firstPageData);
            readFreomCache = true;
            // 如果本地缓存有数据，优先使用缓存的数据
            const newData = firstPageData?.card_list?.length > 0 ? firstPageData : nsfw ? nsfwData : normalData
            if (newData) {
                updatePageData((draft: any) => {
                    draft.roles = newData.card_list
                    draft.count = newData.count
                    draft.offset = 0
                    draft.fetchedOffsets = []
                    if(newData.currentTag) {
                        draft.currentTag = newData.currentTag
                        draft.tags = genTags(newData.currentTag)
                        draft.sort_type = newData.currentTag
                    }
                })
            }
        }
        prevEnabledNSFWRef.current = enabledNSFW;
    }, [enabledNSFW, firstPageData])


    const toggleReg = async () => {
        const isNSFW = !nsfw;
        reset();
        try {
            setEnabledNSFW(isNSFW)
            resetWithNSFWAwitch(isNSFW)
        } catch (e) {
            setEnabledNSFW(!isNSFW)
            resetWithNSFWAwitch(isNSFW)
        }
    }
    const reset = useCallback(() => {
        updatePageData((draft: any) => {
            draft.offset = 0
            draft.fetchedOffsets = []
            // 重置时不清除滚动位置，因为这是用户手动切换标签的情况
            // 如果需要重置滚动位置，请取消下面这行的注释
            // draft.scrollPosition = 0
        })
    }, []);
    const retry = () => {
        mutate()
    }

    // 礼包逻辑
    const confirm = useComfirm('gift');
    const fetchGift = async () => {
        const data = await request('/gift');
        if (data.length > 0) {
            while (data.length > 0) {
                const gift = data.pop();
                const res = await confirm.show({
                    title: gift.desc,
                    desc: t('app.chat.img_desc'),
                    comfirmBtn: t('app.common.comfirm'),
                    showCancelBtn: false,
                    icon: <Image src={'/gift2.png'} width={80} height={80} alt='gift' />,
                });
            }
        }
    }

    useEffect(() => {
        isLogin && fetchGift();
    }, [isLogin])

    useEffect(() => {
        setVisitNewTime(parseInt(localStorage.getItem(VIST_NEW_TAB_TIME) || '0'))
    }, [])

    const toggleBlurImg = () => {
        const isShowBlurImg = !user?.show_nsfw_image;
        updateSetting({
            data: {
                show_nsfw_image: isShowBlurImg
            },
            request,
            auth,
            t
        })
    }
    const switchSubtags = (subtag: string) => {
        const tagArr = pageData.currentSubtag === '' ? [] : pageData.currentSubtag.split(',');
        updatePageData((draft: any) => {
            if (tagArr.indexOf(subtag) !== -1) {
                tagArr.splice(tagArr.indexOf(subtag), 1);
            } else {
                tagArr.push(subtag);
            }
            // console.log('tagArr', tagArr.join(','), subtag);
            // 默认标签，于首次缓存区分
            draft.currentTag = currentTag ? currentTag : pageData?.tags[0].key
            draft.currentSubtag = tagArr.join(',')
        })
        reset();
        document.documentElement.scrollTop = 0;
    }
    const onFilter = ({ subtag, chat_model_id, chat_type, play_type, sort_type, role_gender }: any) => {
        updatePageData((draft: any) => {
            // 默认标签，于首次缓存区分
            draft.currentTag = currentTag ? currentTag : pageData?.tags[0].key
            subtag !== undefined && (draft.currentSubtag = subtag)
            chat_model_id !== undefined && (draft.chat_model_id = chat_model_id)
            chat_type !== undefined && (draft.chat_type = chat_type)
            play_type !== undefined && (draft.play_type = play_type)
            sort_type !== undefined && (draft.sort_type = sort_type)
            role_gender !== undefined && (draft.role_gender = role_gender)
        })
        reset();
        document.documentElement.scrollTop = 0;
    }

    // 自动补充加载，解决内容不足一屏时无法滚动的问题
    useEffect(() => {
        // 不满一屏的情况，最多自动加载到80条数据
        if (hasMore && containerRef.current && pageData.roles.length < 80) {
            const container = containerRef.current;
            // 内容高度不足一屏时自动加载
            if (container.scrollHeight <= container.clientHeight) {
                loadMore();
            }
        }
    }, [pageData.roles.length, hasMore]);
    const handleScroll = debounce(() => {
        // 保存当前滚动位置到页面状态
        if (containerRef.current) {
            updatePageData((draft: any) => {
                draft.scrollPosition = containerRef.current?.scrollTop || 0;
            });
        }
    }, 50);
    return <main
        id="scrollableDiv"
        ref={containerRef}
        className="main-height lg:px-4"
        onScroll={handleScroll}
    >
        {pageData?.tags?.length > 0 &&
            <div className='con-width'><div className='pt-3 mx-2 flex items-center'>
                <Search nsfw={nsfw} />
                <div className='space-y-0.5'>
                    {!config?.sfwApp && enabledNSFW !== null && <label htmlFor="nsfw" className='flex items-center text-xs dark:text-gray-300 text-gray-600'>
                        <Switch
                            id='nsfw'
                            checked={nsfw}
                            onChange={() => { }}
                            onClick={() => { toggleReg() }}
                            className="group inline-flex h-4 w-7 items-center rounded-full bg-gray-300 dark:bg-gray-500 transition data-[checked]:bg-purple-500 dark:data-[checked]:bg-purple-500 mr-1"
                        >
                            <span className="size-3.5 translate-x-0 rounded-full bg-white transition group-data-[checked]:translate-x-3.5" />
                        </Switch>
                        <span>{t('app.index.nsfw')}</span>
                    </label>}
                    {!config?.sfwApp && enabledNSFW !== null &&
                        <label htmlFor="blurimg" className='flex items-center text-xs dark:text-gray-300 text-gray-600'>
                            <div className='flex items-center justify-between'>
                                <Switch
                                    id='blurimg'
                                    checked={!user?.show_nsfw_image}
                                    onClick={toggleBlurImg}
                                    className="group inline-flex h-4 w-7 items-center rounded-full bg-gray-300 dark:bg-gray-500 transition data-[checked]:bg-purple-500 dark:data-[checked]:bg-purple-500 mr-1"
                                >
                                    <span className="size-3.5 translate-x-0 rounded-full bg-white transition group-data-[checked]:translate-x-3.5" />
                                </Switch>
                                {t('app.mine.blur_img')}
                            </div>
                        </label>}
                </div>
            </div>
                <div>
                    <Tabs pageData={pageData} currentTag={currentTag} updatePageData={updatePageData} reset={reset} setVisitNewTime={setVisitNewTime} visitNewTime={visitNewTime} />
                    <Filter onFilter={onFilter} pageData={pageData} currentSubtag={currentSubtag} switchSubtags={switchSubtags} />
                    {firstPageData !== null && <InfiniteScroll
                        dataLength={pageData.roles.length}
                        next={loadMore}
                        hasMore={hasMore}
                        loader={pageData.roles.length > 0 ? <div className="text-center text-xs mb-3">{t('app.index.loading')}...</div> : null}
                        endMessage={
                            !hasMore && !isLoading ? (
                                <div className="text-center text-xs mb-3">{t('app.index.no_card_load')}</div>
                            ) : null
                        }
                        scrollableTarget="scrollableDiv"
                    >
                        <div className="px-2 pt-2 pb-2 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-3 lg:grid-cols-4 xl:grid-cols-5">
                            <TabContent roles={pageData.roles} switchSubtags={switchSubtags} />
                        </div>
                    </InfiniteScroll>}
                </div>
            </div>
        }
        {/* 如果服务端预请求失败，会客户端请求，这里会渲染，并且有卡顿动画，说明服务端预请求失败 */}
        {((isLoading && offset == 0) || enabledNSFW === null) && <LoadingToast msg={t('app.common.loading')} />}
        {error && !isLoading && <LoadingToastFailed retry={retry} error={error} />}
    </main>
        ;
};

export default TabSwitcher;