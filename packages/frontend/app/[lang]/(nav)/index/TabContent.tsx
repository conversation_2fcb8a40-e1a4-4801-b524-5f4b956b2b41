// app/components/ui/TabContent.tsx
"use client";
import React from 'react';
import Card from '@little-tavern/shared/src/ui/card';
import type { RoleType } from '@little-tavern/shared/src/ui/card';
import NoContent from '@share/src/ui/noContent';

type TabContentProps = {
    roles: RoleType[];
    switchSubtags: any
};

const TabContent = ({ roles, switchSubtags }: TabContentProps) => {
    return (
        <>
            {roles?.map((role: any) => {
                if(role.mode_type === 'single') {
                    return <Card key={'single' + role.mode_target_id} role={{...role.role, isShowTags: true}} isBlurImg={true} switchSubtags={switchSubtags}></Card>
                } else {
                    return <Card key={'group' + role.mode_target_id} modeType={role.mode_type} role={{...role.group, isShowTags: true, showCopyRight: true}} isBlurImg={true} switchSubtags={switchSubtags}></Card>
                }
            })}
            {roles?.length === 0 && <div className='col-start-1 sm:col-span-2 lg:col-span-4 xl:col-span-5 flex justify-center w-full'><NoContent /></div>}
        </>
    );
};

export default TabContent;
