'use client'
import type { FC } from 'react'
import React from 'react'
import cn from 'classnames'
import LoginInfo from './loginInfo'
import LangSwitch from '@/app/[lang]/components/langSwitch'
import Gift from './gift'
import ThemeSwitch from './theme/theme-switch'
import NavHeader from './nav-header'
import { Camera } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { tgWeb } from '@/app/module/global'
export type IHeaderProps = {
  lang: string
}
const Header: FC<IHeaderProps> = ({ lang }) => {
  const { t } = useTranslation()
  return (
    <>
      <div className="fixed z-10 top-0 w-full left-0 dark:border-b dark:border-[#2c2e2d] space-x-2 bg-white dark:bg-[var(--background)] drop-shadow-u dark:drop-shadow-none">
        <div className='mx-auto flex items-center justify-between h-[43px]'>
          <NavHeader lang={lang} />
          <div className='flex items-center'>
            {!tgWeb && <Link href={`https://t.me/AITupian_Bot`} target='_blank' className='flex px-1.5 text-sm items-center py-4 relative text-zinc-500 text-center text-gray-500 cursor-pointer'>
              <Camera className='w-4 h-4 mr-0.5' />{t('app.common.gen_img')}
            </Link>}
            <Gift />
            {/* <Link href={`/${lang}/message`} className='flex px-1.5 text-sm items-center py-4 relative text-zinc-500 text-center text-gray-500 cursor-pointer'>
              <div className='absolute left-4 top-4 w-1.5 h-1.5 bg-red-500 rounded-full' />
              <Mail className='w-4 h-4 mr-0.5' />{t('app.common.message')}
            </Link> */}
            <div className={cn('px-1.5 text-gray-500 font-normal text-sm items-center')}>
              <ThemeSwitch></ThemeSwitch>
            </div>
            <LangSwitch />
            <LoginInfo />
          </div>
        </div>
      </div>
    </>
  )
}

export default React.memo(Header, (prevProps, nextProps) => {
  return prevProps.lang === nextProps.lang
})