import { useTranslation } from 'react-i18next'
import { EventType } from './switchModel'

const ModeChangeHistory = ({msgId, modelEventMap}: any) => {
    const { t } = useTranslation()
    // console.log('modelEventMap', modelEventMap, msgId)
    return <>
        {
            msgId && modelEventMap && modelEventMap[msgId] && <div className='text-center text-gray-500 text-xs my-2 w-3/4 mx-auto'>
                {modelEventMap[msgId].map((item: any, index: number) => {
                    const fromBenefitType = item.from_chat_channel === 'FREE_BENEFIT'? t('app.chat.free_benifit') : t('app.chat.paid_benifit')
                    const toBenefitType = item.to_chat_channel === 'FREE_BENEFIT'? t('app.chat.free_benifit') : t('app.chat.paid_benifit')
                    if (item.event_type === EventType.chat_entry) {
                        return <div key={index}>{t('app.chat.tip_change_mode_entry', {to_model_name: item.to_model_name})}（{toBenefitType}）</div>
                    } else if(item.event_type === EventType.chat_man) {
                        return <div key={index}>{t('app.chat.tip_change_mode_man', {to_model_name: item.to_model_name})}（{toBenefitType}）</div>
                    } else if(item.event_type === EventType.chat_auto) {
                        return <div key={index}>{t('app.chat.tip_change_mode_auto', {from_model_name: item.from_model_name + `（${fromBenefitType}）`, to_model_name: item.to_model_name + `（${toBenefitType}）`})}</div>
                    } else if(item.event_type === EventType.chat_channel_auto) {
                        return <div key={index}>{t('app.chat.tip_change_mode_auto1', {from_model_name: item.from_model_name + `（${fromBenefitType}）`, to_model_name: item.to_model_name + `（${toBenefitType}）`})}</div>
                    } else if(item.event_type === EventType.chat_auto_free) {
                        return <div key={index}>{t('app.chat.tip_change_mode_auto_free', {from_model_name: item.from_model_name + `（${fromBenefitType}）`, to_model_name: item.to_model_name + `（${toBenefitType}）`})}</div>
                    }
                })}
            </div>
        }
    </>
}

export default ModeChangeHistory