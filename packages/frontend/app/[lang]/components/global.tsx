'use client'

import React, { useEffect, useRef, useContext } from "react";
import type { FC, ReactNode } from 'react'
import { usePathname } from 'next/navigation';
import useDialogAlert from "@little-tavern/shared/src/hook/useDialogAlert";
import { useTranslation } from 'react-i18next'
import { useRouter, useParams, useSelectedLayoutSegment } from "next/navigation";
import useRequest from "@little-tavern/shared/src/hook/useRequest";
import { useState } from "react";
import {runsOnServerSide} from '@little-tavern/shared/src/module/global'
import { AuthContext } from "@share/src/authContext";
import Toast from "@share/src/ui/toast";

interface IGlobal {
    children: ReactNode
}
// 全局弹窗等操作
const Global: FC<IGlobal> = ({ children }) => {
    const pathname = usePathname();
    const previousPathname = useRef<string | null>(null); // 用于保存上一个路径
    const dialogAlert = useDialogAlert();
    const { t } = useTranslation()
    const router = useRouter();
    const param = useParams();
    const lang = param.lang;
    const request = useRequest();
    const auth = useContext(AuthContext);
    const [isShowGuideCreateCard, setIsShowGuideCreateCard] = useState(!runsOnServerSide && localStorage.getItem('guideCreateCard') === null)
    const selectedSegment = useSelectedLayoutSegment()
    const isLogin = auth?.isLogin;

    useEffect(() => {
        if (previousPathname.current !== null) {
            // console.log('上一个页面的路径是:', previousPathname.current);
        }
        // 在路径变化后，保存当前路径为上一个路径
        previousPathname.current = pathname;
        console.log('page switch:', pathname);
        selectedSegment === null && isLogin && isShowGuideCreateCard && guideCreateCard()
    }, [pathname, isLogin]);

    useEffect(() => {
        if(isLogin) {
            globalNotice()
            diamondSeasonNotice()
        }
    }, [isLogin]);

    // 检查引导到创建卡片
    const guideCreateCard = async () => {
        // 你想在页面切换时执行的代码
        // console.log('页面已切换，执行操作');
        const res = await request('/operation/popup?popup_type=role_create');
        const popup = res?.popup;
        if(popup?.type === 'role_create') {
            const res = await dialogAlert.show({
                desc: popup?.content,
                alertStatus: '2',
                comfirmBtn: popup?.button_text
            })
            setIsShowGuideCreateCard(false)
            localStorage.setItem('guideCreateCard', '1')
            console.log('res', res);
            if(res) {
                router.push(`/${lang}/${popup?.button_link}`)
            }
        }
    };
    // 全局通知
    const globalNotice = async () => {
        const res = await request('/operation/popup?popup_type=common&popup_position=HOME');
        const popup = res?.popup;
        if(popup?.type === 'common') {
            const res = await dialogAlert.show({
                title: popup?.title,
                desc: popup?.content,
                alertStatus: '2',
                comfirmBtn: popup?.button_text,
                isCloseIconShow: popup?.show_close_icon,
                link: popup?.button_action_type === 'outer_link' ? popup?.button_link_url : '' 
            })
            if(popup?.button_action_type === 'inner_link') {
                router.push(`/${lang}/${popup?.button_link_url}`)
            }
        }
    };
    // 钻石充返通知
    const diamondSeasonNotice = async () => {
        const res = await request('/user/diamond_season_activity/popup');
        if(res.error_code === 0) {
            const data = res.data;
            const task_id = data?.task_id;
            if(data !== null) {
                // 报名成功弹窗
                if(data.type === 'd_act_enroll_succ') {
                    dialogAlert.show({
                        desc: data.msg,
                        alertStatus: '2',
                        isMd: true,
                        comfirmBtn: t('app.common.confirm1')
                    })
                // 提醒用户还没有报名的弹窗
                } else if(data.type === 'd_act_notice') {
                    const res = await dialogAlert.show({
                        desc: data.msg,
                        alertStatus: '2',
                        isMd: true,
                        comfirmBtn: t('app.common.confirm2')
                    })
                    if(res) {
                        const res = await request(`/user/diamond_season_activity/enroll?task_id=${task_id}`, {
                            method: 'POST'
                        })
                        if(res.error_code === 0 && res.data.result) {
                            Toast.notify({
                                type: 'success',
                                message: t('app.common.act_join_success')
                            })
                        } else {
                            Toast.notify({
                                type: 'error',
                                message: t('app.common.act_join_failed')
                            })
                        }
                    }
                }
                
            }
        }
    };

    return <>{children}</>
}

export default Global;