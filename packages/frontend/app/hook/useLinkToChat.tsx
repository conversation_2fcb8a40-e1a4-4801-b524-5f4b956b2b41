import { useParams, usePathname, useRouter } from "next/navigation";
// cid:conversation_id
// sid:user_snapshot_id
// newStart:0,读取聊天历史，newStart=1，新开存档
const geenerateUrl = ({isGroup, id, cid, pathname, lang, sid = '', newStart = 0 }: any) => {
    const searchStr = window.location.search
    const _cid = cid? cid : '';
    // 由于chat页面有切换语言选项，切换后，需要更新来源url，所以增加from
    const fromUrl = encodeURIComponent(`${pathname}${searchStr}`)
    const url = isGroup? `/${lang}/chat?groupid=${id}&from=${fromUrl}&cid=${_cid}` : `/${lang}/chat?roleid=${id}&from=${fromUrl}&cid=${_cid}${sid ? `&sid=${sid}` : ''}${newStart ? `&new_start=${newStart}` : ''}`
    return url
}

const useLinkToChat = () => {
    const pathname = usePathname();
    const router = useRouter();
    const params = useParams()
    const lang = params.lang as string
    return {
        push: ({isGroup, id, cid, sid, newStart }: any) => {
            router.push(geenerateUrl({isGroup, id, cid, pathname, lang, sid, newStart }))
        },
        prefetch: ({isGroup, id, cid, sid, newStart }: any) => {
            router.prefetch(geenerateUrl({isGroup, id, cid, pathname, lang, sid, newStart }))
        }
    }
}

export default useLinkToChat