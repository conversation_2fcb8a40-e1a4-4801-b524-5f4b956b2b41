const isMobileDevice = function() {
    if (typeof navigator !== 'undefined') {
        const userAgent = navigator.userAgent.toLowerCase();
        return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
    }
    return false;
}()

const web = process.env.NEXT_PUBLIC_WEB === 'true';
// 英文版小程序
const tgWeb = process.env.NEXT_PUBLIC_TG_WEB === 'true';
const customerBot = tgWeb? 'https://t.me/uhoney_help01_bot' : 'https://t.me/ai_x01_bot';
export {
    isMobileDevice,
    web,
    tgWeb,
    customerBot
}