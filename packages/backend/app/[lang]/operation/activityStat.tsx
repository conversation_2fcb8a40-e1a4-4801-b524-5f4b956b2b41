import format from '@little-tavern/shared/src/module/formate-date'
import Anouncement from './anouncement';
import { useEffect, useState } from 'react';
import Toast from '@share/src/ui/toast';
import useRequest from '../hook/useRequest'
import { DatePicker, Space, Pagination } from 'antd'
import { set } from 'lodash';
import TranslateDetail from './translateDetail';

const ActivityStat = () => {
  const request = useRequest();
  const [startDate, setStartDate] = useState<any>(null);
  const [endDate, setEndDate] = useState<any>(null);
  const [headers, setHeaders] = useState<any>([]);
  const [dataList, setDataList] = useState<any>([]);

  const searchList = async () => {
    Toast.showLoading('')
    let url = `/stat/activity/role/publish_card?`
    url += `start_date=${startDate ? startDate.format('YYYY-MM-DD') : ''}`
    url += `&end_date=${endDate ? endDate.format('YYYY-MM-DD') : ''}`
    // 这里可以添加其他查询参数
    const res = await request(url, {
      method: 'GET'
    })
    if (res.error_code === 0) {
      // 成功处理数据
      const headers = res.data.headers
      const data_list = res.data.data_list
      setHeaders(headers);
      setDataList(data_list);
      // 这里可以更新状态或进行其他操作
    } else {
      Toast.notify({
        type: 'error',
        message: res.message || '查询失败'
      });
    }
    Toast.hideLoading()
  }
  const downloadList = async () => {
    //直接下载headers和dataList
    if (dataList.length === 0) {
      Toast.notify({
        type: 'error',
        message: '没有数据可下载'
      });
      return;
    }
    //headers是表头，将双引号转义，每个数据增加双引号
    const escapedHeaders = headers.map((header: string) => `"${header.replace(/"/g, '""')}"`);

    // 创建一个CSV字符串
    const csvContent = [
      escapedHeaders.join(','), // 添加表头
      ...dataList.map((item: any) => item.map((value: any) => `"${value.replace(/"/g, '""')}"`).join(',')) // 添加数据行
    ].join('\n');
    // 创建一个Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    // 创建一个下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    const start_date = startDate ? startDate.format('YYYY-MM-DD') : 'unknown';
    const end_date = endDate ? endDate.format('YYYY-MM-DD') : 'unknown';

    link.download = `activity_stat_${start_date}_${end_date}.csv`;
    // 触发下载
    document.body.appendChild(link);
    link.click();
    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    Toast.notify({
      type: 'success',
      message: '下载成功'
    });
  }
  const handleDateChange = (date: any, dateType: 'start' | 'end') => {
    if (dateType === 'start') {
      setStartDate(date)
    } else {
      setEndDate(date)
    }
  }
  return <div className='mt-2'>
    <h2 className='text-xl'>翻译模块</h2>
    <div className='mt-3'>
      <span>时间区间(UTC+8)(00:00:00 - 23:59:59)：</span>
      {/* 这里是时间选择器，只选择日，不要时分秒 */}
      <Space>
        <DatePicker
          format="YYYY-MM-DD"
          placeholder="开始时间"
          onChange={(date: any) => handleDateChange(date, 'start')}
        />
        <span>至</span>
        <DatePicker
          format="YYYY-MM-DD"
          placeholder="结束时间"
          onChange={(date: any) => handleDateChange(date, 'end')}
          disabledDate={(current: any) =>
            startDate ? current < startDate : false
          }
        />
      </Space>
      {/* 按钮增加边距 */}
      <button className='bg-blue-500 text-white rounded p-1 mr-2 ml-2' onClick={searchList}>查询</button>
      <button className='bg-blue-500 text-white rounded p-1 mr-2 ml-2' onClick={downloadList}>下载</button>
    </div>
    <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-5'>
      <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
        <tr className='text-center text-xs sm:text-sm border-collapse'>
          {headers.map((header: string, index: number) => (
            <th key={index} className='px-2 py-1'>{header}</th>
          ))}          
        </tr>
      </thead>
      <tbody className='text-center'>
        {dataList.map((item: any, index: number) => (
          <tr key={index} className='border-b dark:border-slate-600'>
            {headers.map((header: string, headerIndex: number) => (
              <td key={headerIndex} className='px-2 py-1'>{item[headerIndex]}</td>
            ))}
          </tr>
        ))}
      </tbody>

    </table>
  </div>
}

export default ActivityStat;