'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import s from './style.module.css'
import cn from 'classnames'
import { useTranslation } from 'react-i18next'
import { useParams } from 'next/navigation'
import useRequest from '@backend/app/[lang]/hook/useRequest'
import { AuthContext } from '../components/authContext';
import Toast from '@little-tavern/shared/src/ui/toast';
import useComfirm from '@little-tavern/shared/src/hook/useComfirm';
import { useForm } from 'react-hook-form';
import AnouncementList from './anouncementList';
import ConsumActivities from './consumActivities';
import TranslateList from './translateList';
import ActivityStat from './activityStat';

const Operation = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const request = useRequest();
  const auth = useContext(AuthContext);

  const [selectedIndex, setSelectedIndex] = useState(0)


  const createDiamondTask = () => {
    console.log('createDiamondTask')

  }
  const tabs = [
    {
      title: '弹窗',
    },
    {
      title: '返钻石',
    },
    {
      title: '翻译',
    },
    {
      title: '角色卡活动',
    }
  ]
  
  return (
    <>
      <main className="main-height bg-black py-2">
        <div className='mx-5'>
          <div className='pb-1 py-3'>
            {tabs.map((item: any, index: number) => {
              return <button key={index} type='button' onClick={() => { setSelectedIndex(index) }} className={cn('mr-4 text-lg mb-1', selectedIndex == index && 'border-b-2 border-purple-500')}>{item.title}</button>
            })}
          </div>
          {selectedIndex == 0 && <AnouncementList />}
          {selectedIndex == 1 && <ConsumActivities />}
          {selectedIndex == 2 && <TranslateList />}
          {selectedIndex == 3 && <ActivityStat />}
        </div>
      </main>
    </>
  )
}

export default Operation
