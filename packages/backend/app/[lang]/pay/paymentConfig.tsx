import { useEffect, useState, useCallback } from 'react';
import Toast from '@share/src/ui/toast';
import useRequest from '../hook/useRequest';

type PaymentConfigItem = {
  id: number;
  channel: string;
  pay_type: string;
  min_amount: number;
  max_amount: number;
  ratio: number;
  remark: string;
  enabled: boolean; // 添加是否启用字段
  isEditing?: boolean;
};

type WhitelistItem = {
  tg_id: number;
  channel: string;
  enabled: boolean;
};

type WhitelistByChannel = {
  [channel: string]: WhitelistItem[];
};

const PaymentConfig = () => {
  const request = useRequest();
  const [configList, setConfigList] = useState<PaymentConfigItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [newItem, setNewItem] = useState<PaymentConfigItem | null>(null);
  
  // 排序相关状态
  const [sortConfig, setSortConfig] = useState<{
    key: 'channel' | 'pay_type' | null;
    direction: 'asc' | 'desc';
  }>({ key: null, direction: 'asc' });

  // 白名单相关状态
  const [whitelistData, setWhitelistData] = useState<WhitelistByChannel>({});
  const [whitelistLoading, setWhitelistLoading] = useState(false);
  const [newWhitelistItem, setNewWhitelistItem] = useState<{ tg_id: string; channel: string }>({ tg_id: '', channel: '' });
  const [showAddWhitelist, setShowAddWhitelist] = useState(false);
  const [activeTab, setActiveTab] = useState<'payment' | 'whitelist'>('payment');
  const [channels, setChannels] = useState<string[]>([]);
  const [channelsLoading, setChannelsLoading] = useState(false);

  // 获取配置列表
  const getConfigList = useCallback(async () => {
    setLoading(true);
    try {
      const res = await request(`/channel_control/list`);
      setConfigList(res.controls);
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '获取支付配置失败'
      });
    } finally {
      setLoading(false);
    }
  }, [request]);

  // 获取白名单列表
  const getWhitelistData = useCallback(async () => {
    setWhitelistLoading(true);
    try {
      const res = await request(`/channel_control/whitelist`);
      const whitelist = res.whitelist;

      // 按 channel 分组
      const groupedData: WhitelistByChannel = {};
      whitelist.forEach((item: WhitelistItem) => {
        if (!item.enabled) return;

        if (!groupedData[item.channel]) {
          groupedData[item.channel] = [];
        }
        groupedData[item.channel].push(item);
      });

      setWhitelistData(groupedData);
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '获取白名单失败'
      });
    } finally {
      setWhitelistLoading(false);
    }
  }, [request]);

  // 获取渠道列表
  const getChannels = useCallback(async () => {
    setChannelsLoading(true);
    try {
      const res = await request(`/channel_control/channels`);
      setChannels(res.channels);
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '获取渠道列表失败'
      });
    } finally {
      setChannelsLoading(false);
    }
  }, [request]);

  // 添加白名单
  const handleAddWhitelist = async () => {
    if (!newWhitelistItem.tg_id || !newWhitelistItem.channel) {
      Toast.notify({
        type: 'error',
        message: '请填写完整信息'
      });
      return;
    }

    try {
      await request(`/channel_control/whitelist/add`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tg_id: parseInt(newWhitelistItem.tg_id),
          channel: newWhitelistItem.channel
        })
      });

      Toast.notify({
        type: 'success',
        message: '添加成功'
      });

      setNewWhitelistItem({ tg_id: '', channel: '' });
      setShowAddWhitelist(false);
      getWhitelistData();
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '添加失败'
      });
    }
  };

  // 删除白名单
  const handleDeleteWhitelist = async (tg_id: number, channel: string) => {
    if (!confirm('确定要删除这个白名单项吗？')) {
      return;
    }

    try {
      await request(`/channel_control/whitelist/delete/${tg_id}/${channel}`, {
        method: 'POST'
      });

      Toast.notify({
        type: 'success',
        message: '删除成功'
      });

      getWhitelistData();
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '删除失败'
      });
    }
  };

  // 启用白名单（调用 add 接口）
  const handleEnableWhitelist = async (tg_id: number, channel: string) => {
    try {
      await request(`/channel_control/whitelist/add`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tg_id: tg_id,
          channel: channel
        })
      });

      Toast.notify({
        type: 'success',
        message: '启用成功'
      });

      getWhitelistData();
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '启用失败'
      });
    }
  };

  // 添加新项目
  const handleAddItem = () => {
    setNewItem({
      id: -1,
      channel: '',
      pay_type: '',
      min_amount: 0,
      max_amount: 0,
      ratio: 100,
      remark: '',
      enabled: true, // 默认启用
      isEditing: true
    });
  };

  // 保存项目
  const handleSaveItem = async (item: PaymentConfigItem) => {
    try {
      // 这里使用 mock 数据，实际项目中应该调用真实接口
      // const res = await request(`/payment_config/toggle/${id}`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ enabled: updatedItem.enabled })
      // });
      
      if (newItem && item.id === newItem.id) {
        const res = await request(`/channel_control/add`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(item)
        });

        setConfigList([...configList, { ...item, isEditing: false }]);
        setNewItem(null);
      } else {
        const res = await request(`/channel_control/update/${item.id}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(item)
        });

        setConfigList(configList.map(config => 
          config.id === item.id ? { ...item, isEditing: false } : config
        ));
      }
      
      Toast.notify({
        type: 'success',
        message: '保存成功'
      });
    } catch (e) {
      Toast.notify({
        type: 'error',
        message: '保存失败'
      });
    }
  };

  // 取消编辑
  const handleCancelEdit = (id: number) => {
    if (newItem && id === newItem.id) {
      setNewItem(null);
    } else {
      setConfigList(configList.map(item => 
        item.id === id ? { ...item, isEditing: false } : item
      ));
    }
  };

  // 开始编辑
  const handleStartEdit = (id: number) => {
    setConfigList(configList.map(item => 
      item.id === id ? { ...item, isEditing: true } : item
    ));
  };

  // 处理输入变化
  const handleInputChange = (id: number, field: keyof PaymentConfigItem, value: string | number | boolean) => {
    if (newItem && id === newItem.id) {
      setNewItem({ ...newItem, [field]: value });
    } else {
      setConfigList(configList.map(item => 
        item.id === id ? { ...item, [field]: value } : item
      ));
    }
  };

  // 排序函数
  const handleSort = (key: 'channel' | 'pay_type') => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    
    setSortConfig({ key, direction });
    
    const sortedList = [...configList].sort((a, b) => {
      if (a[key] < b[key]) {
        return direction === 'asc' ? -1 : 1;
      }
      if (a[key] > b[key]) {
        return direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
    
    setConfigList(sortedList);
  };

  // 获取排序图标
  const getSortIcon = (key: 'channel' | 'pay_type') => {
    if (sortConfig.key !== key) {
      return <span className="ml-1 text-gray-500">↕️</span>;
    }
    return sortConfig.direction === 'asc' ? 
      <span className="ml-1 text-blue-400">↑</span> : 
      <span className="ml-1 text-blue-400">↓</span>;
  };

  useEffect(() => {
    getConfigList();
    getWhitelistData();
    getChannels();
  }, [getConfigList, getWhitelistData, getChannels]);

  // 渲染表格行
  const renderTableRow = (item: PaymentConfigItem) => {
    const isEditing = item.isEditing;
    
    return (
      <tr key={item.id} className="ml-3 my-1 border-1 text-gray-400">
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="text"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.channel}
              disabled={item.id !== newItem?.id} // 只有新增时可编辑
              onChange={(e) => handleInputChange(item.id, 'channel', e.target.value)}
            />
          ) : (
            item.channel
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="text"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.pay_type}
              disabled={item.id !== newItem?.id} // 只有新增时可编辑
              onChange={(e) => handleInputChange(item.id, 'pay_type', e.target.value)}
            />
          ) : (
            item.pay_type
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="number"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.ratio}
              onChange={(e) => handleInputChange(item.id, 'ratio', parseInt(e.target.value) || 0)}
            />
          ) : (
            item.ratio
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="number"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.min_amount}
              onChange={(e) => handleInputChange(item.id, 'min_amount', parseInt(e.target.value) || 0)}
            />
          ) : (
            item.min_amount
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="number"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.max_amount}
              onChange={(e) => handleInputChange(item.id, 'max_amount', parseInt(e.target.value) || 0)}
            />
          ) : (
            item.max_amount
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1 text-center">
          {isEditing ? (
            <input
              type="checkbox"
              className="h-4 w-4"
              checked={item.enabled}
              onChange={(e) => handleInputChange(item.id, 'enabled', e.target.checked)}
            />
          ) : (
            <div className="flex justify-center">
              <span 
                className={`inline-block w-3 h-3 rounded-full ${item.enabled ? 'bg-green-500' : 'bg-red-500'}`}
              ></span>
            </div>
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          {isEditing ? (
            <input
              type="text"
              className="bg-gray-800 w-full px-2 py-1"
              value={item.remark}
              onChange={(e) => handleInputChange(item.id, 'remark', e.target.value)}
            />
          ) : (
            item.remark
          )}
        </td>
        <td className="border border-slate-700 py-1 px-1">
          <div className="flex flex-wrap gap-1">
            {isEditing ? (
              <>
                <button
                  type="button"
                  className="p-1 px-3 bg-green-500 text-white rounded text-sm"
                  onClick={() => handleSaveItem(item)}
                >
                  保存
                </button>
                <button
                  type="button"
                  className="p-1 px-3 bg-gray-500 text-white rounded text-sm"
                  onClick={() => handleCancelEdit(item.id)}
                >
                  取消
                </button>
              </>
            ) : (
              <button
                type="button"
                className="p-1 px-3 bg-sky-500 text-white rounded text-sm"
                onClick={() => handleStartEdit(item.id)}
              >
                编辑
              </button>
            )}
          </div>
        </td>
      </tr>
    );
  };

  // 渲染白名单添加表单
  const renderAddWhitelistForm = () => (
    <div className="mb-4 p-4 border border-slate-600 rounded bg-gray-800">
      <h3 className="text-white mb-3">添加白名单</h3>
      <div className="flex gap-3 items-end">
        <div>
          <label className="block text-gray-300 text-sm mb-1">TG ID</label>
          <input
            type="number"
            className="bg-gray-700 text-white px-3 py-2 rounded border border-slate-600"
            value={newWhitelistItem.tg_id}
            onChange={(e) => setNewWhitelistItem({ ...newWhitelistItem, tg_id: e.target.value })}
            placeholder="输入 TG ID"
          />
        </div>
        <div>
          <label className="block text-gray-300 text-sm mb-1">渠道</label>
          <select
            className="bg-gray-700 text-white px-3 py-2 rounded border border-slate-600"
            value={newWhitelistItem.channel}
            onChange={(e) => setNewWhitelistItem({ ...newWhitelistItem, channel: e.target.value })}
          >
            <option value="">请选择渠道</option>
            {channelsLoading ? (
              <option value="" disabled>加载中...</option>
            ) : (
              channels.map((channel) => (
                <option key={channel} value={channel}>
                  {channel}
                </option>
              ))
            )}
          </select>
        </div>
        <div className="flex gap-2">
          <button
            type="button"
            className="px-4 py-2 bg-green-500 text-white rounded text-sm"
            onClick={handleAddWhitelist}
          >
            添加
          </button>
          <button
            type="button"
            className="px-4 py-2 bg-gray-500 text-white rounded text-sm"
            onClick={() => setShowAddWhitelist(false)}
          >
            取消
          </button>
        </div>
      </div>
    </div>
  );

  // 渲染白名单表格
  const renderWhitelistTable = () => (
    <div className="mt-4">
      <div className="flex justify-between items-center mb-3">
        <h2 className="text-white text-lg">白名单管理</h2>
        <button
          type="button"
          className="p-2 px-5 bg-blue-500 text-white rounded text-sm"
          onClick={() => setShowAddWhitelist(true)}
        >
          添加白名单
        </button>
      </div>

      {showAddWhitelist && renderAddWhitelistForm()}

      {whitelistLoading ? (
        <div className="text-center text-gray-400 py-4">加载中...</div>
      ) : (
        <div className="space-y-6">
          {Object.keys(whitelistData).length === 0 ? (
            <div className="text-center text-gray-400 py-4">暂无白名单数据</div>
          ) : (
            Object.entries(whitelistData).map(([channel, items]) => (
              <div key={channel} className="border border-slate-600 rounded">
                <div className="bg-slate-700 px-4 py-2 text-white font-medium">
                  渠道: {channel} ({items.length} 个白名单)
                </div>
                <table className="w-full text-gray-400 text-sm">
                  <thead className="bg-slate-800">
                    <tr>
                      <th className="border border-slate-600 py-2 px-4 text-left">TG ID</th>
                      <th className="border border-slate-600 py-2 px-4 text-center">状态</th>
                      <th className="border border-slate-600 py-2 px-4 text-center">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {items.map((item) => (
                      <tr key={`${item.tg_id}-${item.channel}`} className="hover:bg-slate-800">
                        <td className="border border-slate-600 py-2 px-4">{item.tg_id}</td>
                        <td className="border border-slate-600 py-2 px-4 text-center">
                          <span
                            className={`inline-block w-3 h-3 rounded-full ${item.enabled ? 'bg-green-500' : 'bg-red-500'}`}
                          ></span>
                        </td>
                        <td className="border border-slate-600 py-2 px-4 text-center">
                          <div className="flex gap-2 justify-center">
                            {!item.enabled && (
                              <button
                                type="button"
                                className="px-3 py-1 bg-green-500 text-white rounded text-sm"
                                onClick={() => handleEnableWhitelist(item.tg_id, item.channel)}
                              >
                                启用
                              </button>
                            )}
                            <button
                              type="button"
                              className="px-3 py-1 bg-red-500 text-white rounded text-sm"
                              onClick={() => handleDeleteWhitelist(item.tg_id, item.channel)}
                            >
                              删除
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );

  return (
    <div className="mt-2">
      {/* Tab 切换 */}
      <div className="flex mb-4 border-b border-slate-600">
        <button
          type="button"
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'payment'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('payment')}
        >
          支付配置
        </button>
        <button
          type="button"
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'whitelist'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-white'
          }`}
          onClick={() => setActiveTab('whitelist')}
        >
          白名单管理
        </button>
      </div>

      {/* 支付配置内容 */}
      {activeTab === 'payment' && (
        <div>
          <button
            type="button"
            className="p-2 px-5 bg-purple-500 text-white rounded text-sm mb-3"
            onClick={handleAddItem}
          >
            新增配置
          </button>

          <table className="dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2 w-full">
            <thead className="dark:bg-slate-700 bg-slate-300 dark:text-slate-300">
              <tr className="text-center text-xs sm:text-sm">
                <th className="border py-2 px-1 border-slate-600">
                  <button
                    type="button"
                    className="flex items-center justify-center w-full hover:text-blue-400 transition-colors"
                    onClick={() => handleSort('channel')}
                  >
                    渠道
                    {getSortIcon('channel')}
                  </button>
                </th>
                <th className="border py-2 px-4 border-slate-600">
                  <button
                    type="button"
                    className="flex items-center justify-center w-full hover:text-blue-400 transition-colors"
                    onClick={() => handleSort('pay_type')}
                  >
                    付款方式
                    {getSortIcon('pay_type')}
                  </button>
                </th>
                <th className="border py-2 px-4 border-slate-600">比例</th>
                <th className="border py-2 px-4 border-slate-600">最小金额</th>
                <th className="border py-2 px-4 border-slate-600">最大金额</th>
                <th className="border py-2 px-4 border-slate-600">是否启用</th>
                <th className="border py-2 px-4 border-slate-600">备注</th>
                <th className="border py-2 px-3 border-slate-600">操作</th>
              </tr>
            </thead>
            <tbody>
              {configList.map(item => renderTableRow(item))}
              {newItem && renderTableRow(newItem)}
            </tbody>
          </table>
        </div>
      )}

      {/* 白名单管理内容 */}
      {activeTab === 'whitelist' && renderWhitelistTable()}
    </div>
  );
};

export default PaymentConfig;
