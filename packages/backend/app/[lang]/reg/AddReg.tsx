"use client";
import React, { useEffect, useRef, useState } from 'react';
import type { FC, FormEvent } from 'react'
import Modal from '@little-tavern/shared/src/ui/dialog/Modal';
import Toast from '@little-tavern/shared/src/ui/toast';
import useComfirm from '@little-tavern/shared/src/hook/useComfirm';
import { useForm } from 'react-hook-form';
import useRequest from '@backend/app/[lang]/hook/useRequest'
import { useTranslation } from 'react-i18next'

type IProps = {
  onCancel: Function,
  onConfirm: Function,
  isOpen: boolean,
  msg?: Record<string, any>
}
type FormData = {
  id?: string,
  name: string
}
// let regAffectOpts = ['前端展示', 'Prompt']
const AddReg: FC<IProps> = ({ onConfirm, onCancel, isOpen, msg }) => {
  const request = useRequest();
  const confirm = useComfirm();
  const [regResult, setRegResult] = useState('');
  const [serverReqResult, setServerReqResult] = useState('');
  const { register, handleSubmit, watch, formState: { errors } }: any = useForm<any>();
  const { t } = useTranslation()
  //原子变量，避免重复请求
  const repeat_request = useRef("");
  const onTest = async (data: any) => {
    const { regex, test_reg_txt, replacement } = data;
    try {
      const [, pattern, flags] = regex.match(/^\/(.*?)\/([sgimuy]*)$/);
      const regexPattern = new RegExp(pattern, flags);
      const replacedText = test_reg_txt.replace(regexPattern, replacement);
      setRegResult(replacedText);
    } catch(e) {
      setRegResult(test_reg_txt);
    }
    try {
      //确保同时只有一个请求
      if (repeat_request.current === regex + test_reg_txt + replacement) {
        return;
      } else if (serverReqResult === 'loading...' || regex === '' || test_reg_txt === '') {
        return;
      } else {
        repeat_request.current = regex + test_reg_txt + replacement;
        //增加重复请求的判断，通过原子变量
        setServerReqResult('loading...');
        const response = await request('/regex/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            regex: regex,
            testRegTxt: test_reg_txt,
            replacement: replacement
          }),
        });
        setServerReqResult(response.data.testRegTxt);
      }
    } catch(e) {
      setServerReqResult("error");
    }
  };
  const onSubmit = async (data: any) => {
    if(typeof data.affects == 'string') {
      data.affects = [data.affects]
    }
    if(typeof data.options == 'string') {
      data.options = [data.options]
    }
    !data.min_depth && (delete data.min_depth)
    !data.max_depth && (delete data.max_depth)
    try {
      const response = await request(msg?.isEdit? `/regex/update` : '/regex/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      Toast.notify({
        type:'success',
        message: `保存成功`
      })
      onConfirm();
    } catch (error) {
      Toast.notify({
        type:'error',
        message: `保存异常`
      })
    }
  };
  const watchFields = watch();

  useEffect(() => {
    onTest(watchFields);
    // console.log(watchFields,handleSubmit);
  }, [watchFields, handleSubmit]);
  return (
    <Modal onClose={() => {onCancel()}} isOpen={isOpen} conWidth='sm:w-[950px]'>
      <div className='min-h-20 mb-4'>
        <div className="flex min-h-full flex-1 flex-col justify-center pt-4">
        <div className="">
        <form className="" action="#" method="POST" onSubmit={handleSubmit(onSubmit)}>
            <div className='max-h-[75vh] overflow-auto space-y-6 mb-4 px-8 pt-4 pb-1'>
              <div className='sm:w-full'>
                <div>
                {msg?.rule_id && <input type="text" {...register('rule_id')} defaultValue={msg?.rule_id} hidden/>}
                <div className="mt-2 flex">
                  标题：<input
                    type="text"
                    id="title"
                    autoComplete="title"
                    required
                    placeholder=''
                    {...register('title')}
                    defaultValue={msg?.title}
                    className="rounded-md border-0 py-2.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 mr-6"
                  />
                </div>
                <div className="mt-2 flex items-center">
                  affects：{msg?.opt.affects.map((tag: any) => {
                    return <label key={tag} className='mr-3 text-gray-400 text-sm'><input className='mr-0.5' {...register("affects", { required: { value: true, message: '需要勾选标签' } })} type="checkbox" value={tag} defaultChecked={msg?.affects?.includes(tag)? true : false} />{tag}</label>
                  })}
                  {errors.affects && (
                      <span className='text-xs mt-1 text-red-500 '>{errors.affects.message}</span>
                    )}
                </div>
                <div className="mt-2 flex items-center">
                  
                all_regex：{msg?.opt.options.map((tag: any) => {
                      return <label key={tag} className='mr-3 text-gray-400 text-sm'><input className='mr-0.5' {...register("options", { required: { value: true, message: '需要勾选标签' } })} type="checkbox" value={tag} defaultChecked={msg?.options?.includes(tag)? true : false} />{tag}</label>
                    })}
                    {errors.options && (
                      <span className='text-xs mt-1 text-red-500 '>{errors.options.message}</span>
                    )}
                </div>
                <div className="mt-2 flex">
                min_depth：<input
                    type="number"
                    id="min_depth"
                    autoComplete="min_depth"
                    defaultValue={msg?.min_depth || -1}
                    {...register('min_depth')}
                    className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-3 bg-gray-800 px-3 w-16 mr-8"
                  />
                  max_depth：<input
                    type="number"
                    id="max_depth"
                    autoComplete="max_depth"
                    defaultValue={msg?.max_depth || -1}
                    {...register('max_depth')}
                    className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-16"
                  />
                </div>
                <div className="mt-2 flex">
                正则：<input
                    type="text"
                    id="regex"
                    autoComplete="regex"
                    required
                    placeholder='请输入要匹配的正则，格式/pattern/flags'
                    {...register('regex')}
                    defaultValue={msg?.regex}
                    className="rounded-md border-0 py-2.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-[600px]"
                  />
                  </div>
                </div>
                <div className="mt-2 flex">
                测试：<textarea
                    id="test_reg_txt"
                    autoComplete="test_reg_txt"
                    placeholder='请输入要匹配的文字'
                    {...register('test_reg_txt')}
                    defaultValue={msg?.test_reg_txt}
                    className="rounded-md border-0 py-2.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-[600px]"
                  />
                </div>
                <div className="mt-2 flex">
                替换：<textarea
                    id="replacement"
                    autoComplete="replacement"
                    placeholder=''
                    {...register('replacement')}
                    defaultValue={msg?.replacement}
                    className="rounded-md border-0 py-2.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-[600px]"
                  />
                </div>
                <div className="mt-2 flex">
                备注：<input
                    type="text"
                    id="description"
                    autoComplete="description"
                    placeholder='请输入备注内容'
                    {...register('description')}
                    defaultValue={msg?.description}
                    className="rounded-md border-0 py-2.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3 w-[600px]"
                  />
                </div>
                <div className="mt-2">
                  <p><b className='ml-2'>JsResult: </b></p>
                  <div className='whitespace-pre'>{regResult}</div>
                </div>
                <div className="mt-2">
                  <p><b className='ml-2'>ServerResult: </b></p>
                  <div className='whitespace-pre'>{serverReqResult}</div>
                </div>
              </div>
            </div>
            <div className='px-8 py-2 flex flex-row-reverse'>
              <button className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' type="submit">{msg?.isEdit? t('app.common.update') : '确定'}</button>
              <button className='p-2 px-5 bg-gray-800 rounded text-sm' type='button' onClick={() => {onCancel()}}>取消</button>
            </div>
          </form>
        </div>
      </div>
      </div>
    </Modal>
  );
};

export default AddReg;