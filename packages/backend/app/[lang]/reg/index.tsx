'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import s from './style.module.css'
import cn from 'classnames'
import { useTranslation } from 'react-i18next'
import { useParams } from 'next/navigation'
import useRequest from '@backend/app/[lang]/hook/useRequest'
import { AuthContext } from '../components/authContext';
import { PencilSquareIcon } from '@heroicons/react/24/solid'
import { Switch } from '@headlessui/react'
import useAddReg from './useAddReg';
import useComfirm from '@little-tavern/shared/src/hook/useComfirm';
import Toast from '@little-tavern/shared/src/ui/toast';
import { LoadingToast } from '@share/src/ui/Loading';

export type IOperation = {
  id?: string,
  type: 'human' | 'ai' | 'img',
  avatar?: string,
  message_id?: string,
  version?: string,
  // 跟message_id一样，缓存判断是否重复请求
  imgId?: string,
  voice_url?: string,
  content: string,
  timestamp: number | null,
  isLoading?: boolean
}

const Operation = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const request = useRequest();
  const confirm = useComfirm();
  const [opt, setOpt] = useState<any>();

  const [regs, setRegs] = useState<any>([])
  const addReg = useAddReg();
  const queryTags = async () => {
    try {
      const res = await request(`/regex/all`)
      if(res?.data.all_regex.length > 0) {
        setRegs(res.data.all_regex)
      }
      setOpt({
        options: res.data.options,
        affects: res.data.affects
      });
    } catch(e) {
      console.log("e", e);
    }
  }
  useEffect(() => {
    queryTags();
  }, [])
  const addHandler = async () => {
    const res = await addReg.show({msg: {opt: opt}});
    console.log('res', res);
    if(res) {
      queryTags();
    }
  }
  const editHandler = async (reg: any) => {
    const res = await addReg.show({msg: {...reg, isEdit: true, opt: opt}});
    if(res) {
      queryTags();
    }
  }
  const toggleReg = async (reg: any) => {
    reg.enabled = !reg.enabled
    try {
      const res = await request('/regex/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reg)
      })
      const newRegs = regs.map((_reg: any) => {
        if(_reg.rule_id == reg.rule_id) {
          _reg.enabled = reg.enabled
        }
        return _reg;
      })
      setRegs(newRegs)
      Toast.notify({
        type:'success',
        message: `操作成功`
      })
    } catch(e) {
      console.log("e", e);
    }
  }
  const updateOrder = async (id:string, order: number) => {
    if(order === 0) return;
    try {
      const res = await request('/regex/update_order?rule_id=' + id + '&order=' + order, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
      })
      queryTags();
    } catch(e) {
      console.log("e", e);
    }
  }
  const delReg = async (id: string) => {
    const res = await confirm.show({});
    if (res.confirm) {
      try {
        const res = await request('/regex/delete?rule_id=' + id, {
          method: 'POST'
        })
        if(res.success) {
          queryTags();
          Toast.notify({
            type:'success',
            message: `删除成功`
          })
        } else {
          Toast.notify({
            type:'error',
            message: `删除失败`
          })
        }
      } catch(e) {
        console.log("e", e);
      }
    }
  }
  return (
    <>
     <main className="main-height bg-black con-width pt-4 px-3">
        <div>
        <h2 className='text-xl'>返回内容正则处理</h2>
          <div className='sm:w-full sm:max-w-[1200px]'>
            <label htmlFor="name" className="block text-sm font-medium leading-6 ">
            正则列表：
            </label>
            <div className="mt-3">
              <table className="bg-gray-900 text-gray-500 text-sm border-collapse w-full">
                <thead className='bg-slate-700 text-slate-300'>
                  <tr>
                    <th scope="col" className="border py-2 px-1 border-slate-600">名称</th>
                    <th scope="col" className="border py-2 px-1 border-slate-600 w-20">状态</th>
                    <th scope="col" className="border py-2 px-1 border-slate-600">操作</th>
                    <th scope="col" className="border py-2 px-1 border-slate-600">正则</th>
                    <th scope="col" className="border py-2 px-1 border-slate-600">描述</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {regs.map((reg: any, index: number) => (
                    <tr key={reg.rule_id} className="hover:bg-gray-800">
                      <td className="border border-slate-700 py-1 px-1">{reg.title}</td>
                      <td className="border border-slate-700 py-1 px-1 text-center">
                        <Switch
                          checked={reg.enabled}
                          onChange={() => {}}
                          onClick={() => {toggleReg(reg)}}
                          className="group inline-flex h-3 w-5 items-center rounded-full bg-gray-500 transition data-[checked]:bg-blue-600"
                        >
                          <span className="size-2 translate-x-0.5 rounded-full bg-white transition group-data-[checked]:translate-x-2.5" />
                        </Switch>
                      </td>
                      <td className="border border-slate-700 py-1 px-1 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <button
                            // onClick={() => { index > 0 && setRegs([...regs.slice(0, index - 1), reg, regs[index - 1], ...regs.slice(index + 1)]) }}
                            onClick={() => { updateOrder(reg.rule_id, -1) }}
                            disabled={index === 0}
                            className="p-1 text-gray-400 hover:text-gray-100 disabled:opacity-50"
                            title="上移"
                          >
                            ↑
                          </button>
                          <button
                            // onClick={() => { index < regs.length - 1 && setRegs([...regs.slice(0, index), regs[index + 1], reg, ...regs.slice(index + 2)]) }}
                            onClick={() => { updateOrder(reg.rule_id, 1) }}
                            disabled={index === regs.length - 1}
                            className="p-1 text-gray-400 hover:text-gray-100 disabled:opacity-50"
                            title="下移"
                          >
                            ↓
                          </button>
                          <button
                            onClick={() => { editHandler(reg) }}
                            type="button"
                            className="p-1 text-gray-400 hover:text-gray-100"
                            title="编辑"
                          >
                            <PencilSquareIcon className="h-4 w-4" aria-hidden="true" />
                          </button>
                          <button
                            onClick={() => { delReg(reg.rule_id) }}
                            type="button"
                            className="p-1 text-gray-400 hover:text-gray-100"
                            title="删除"
                          >
                            ×
                          </button>
                        </div>
                      </td>
                      <td className="border border-slate-700 py-1 px-1">{reg.regex}</td>
                      <td className="border border-slate-700 py-1 px-1">{reg.description}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className='mt-5'>
            <button type='button' onClick={addHandler} className='p-2 px-5 bg-purple-500 text-white rounded text-sm'>添加</button>
          </div>
        </div>
      </main>
    </>
  )
}
export default Operation
