'use client'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import cn from 'classnames'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import s from '@backend/app/[lang]/globals.module.css'
import Loader from '@little-tavern/shared/src/ui/Loading'
import { AuthContext } from '../components/authContext'
import Toast from '@little-tavern/shared/src/ui/toast'
import format from '@little-tavern/shared/src/module/formate-date'
import DiamonHistory from './diamonHistory'
import DiamonConsumeHistory from './diamonConsumeHistory'
import DecreaseDiamon from './decreaseDiamon'
import AddDiamon from './addDiamon'
import useRequest from '../hook/useRequest'
import useComfirm from '@share/src/hook/useComfirm'
import UsdtChargeHistory, { UserUsdtChargeHistory } from './usdtChargeHistory'
import useDialogAlert from '@share/src/hook/useDialogAlert'
import OrderInfo from './orderInfo'
import UpdateUserCard from './updateUserCard'

const btn = cn(s.primBtn, 'mx-2 text-sm')
import ActivityInfo from './activityInfo'
import Link from 'next/link'

const Pay = () => {
  const { t } = useTranslation()
  const params = useParams()
  const lang = params.lang as string
  const auth = useContext(AuthContext);
  const isLogin = auth?.isLogin;
  const request = useRequest();
  const userRef = useRef<any>(null)
  const user1Ref = useRef<any>(null)
  const txIdRef = useRef<any>(null)
  const chainRef = useRef<any>(null)
  const orderIdRef = useRef<any>(null)
  const order1IdRef = useRef<any>(null)
  const [user, setUser] = useState<any>(null)
  const [usdtChargeHistory, setUsdtChargeHistory] = useState<any>(null)
  const [userUsdtChargeHistory, setUserUsdtChargeHistory] = useState<any>(null)
  const [orderInfo, setOrderInfo,] = useState<any>(null)
  const [chains, setChains] = useState<any>([])
  const comfirm = useComfirm()
  const dialogAlert = useDialogAlert()

  useEffect(() => {
    const getChains = async () => {
      const res = await request(`/usdt/chains`)
      res.data.push({ name: 'TRON（trc-20）', value: 'TRON' })
      setChains(res.data)
    }
    getChains()
  }, [])

  const getUserInfo = async () => {
    Toast.showLoading('');
    const res = await request(`/user_info?other_user_id=${userRef?.current?.value}&tg_user_id=0`);
    Toast.hideLoading();
    setUser(res.data)
  }

  const [userActivityInfo, setUserActivityInfo] = useState<any>(null)
  const getUserActivityInfo = async (byId: boolean) => {
    Toast.showLoading('');
    const id = user1Ref?.current?.value
    const query = byId ? `user_id=${id}` : `tg_user_id=${id}`
    const res = await request(`/activity_diamond/user_enroll?${query}`);
    Toast.hideLoading();
    if (res?.error_code === 0) {
      setUserActivityInfo(res.data?.list || [])
    } else {
      Toast.notify({ type: 'error', message: res.message })
    }
  }
  const getUserInfoByTgId = async () => {
    Toast.showLoading('');
    const res = await request(`/user_info?other_user_id=0&tg_user_id=${userRef?.current?.value}`);
    Toast.hideLoading();
    if (res?.error_code !== 0) {
      Toast.notify({ type: 'error', message: res.message })
      return
    }
    setUser(res.data)
  }
  const getOrderById = async () => {
    Toast.showLoading('');
    const res = await request(`/order_by_id?order_id=${order1IdRef?.current?.value}`);
    Toast.hideLoading();
    setOrderInfo(res.data?.order)
  }
  const getOrderByOuterId = async () => {
    Toast.showLoading('');
    const res = await request(`/order_by_out_order_id?out_order_id=${order1IdRef?.current?.value}`);
    Toast.hideLoading();
    if (res?.error_code !== 0) {
      Toast.notify({ type: 'error', message: res.message })
      return
    }
    setOrderInfo(res.data?.order)
  }
  const getUsdtChargeHistory = async () => {
    Toast.showLoading('');
    const res = await request(`/usdt/charge_history`);
    Toast.hideLoading();
    if (res?.error_code !== 0) {
      Toast.notify({ type: 'error', message: res.message })
      return
    } else {
      setUsdtChargeHistory(res.data)
    }
  }

  const getUserUsdtChargeHistory = async () => {
    await doGetUserUsdtChargeHistory(userRef?.current?.value)
  }

  const getCurrentUserUsdtChargeHistory = async () => { 
    await doGetUserUsdtChargeHistory(user?.user_id)
  }

  const doGetUserUsdtChargeHistory = async (userId: any) => {
    Toast.showLoading('');
    const res = await request(`/usdt/user_orders?uid=${userId}`);
    Toast.hideLoading();
    if (res?.error_code !== 0) {
      Toast.notify({ type: 'error', message: res.message })
      return
    } else {
      setUserUsdtChargeHistory(res.data)
    }
  }
  const [showDiamondHistory, setShowDiamondHistory] = useState(false)
  const [showDiamondConsumeHistory, setShowDiamondConsumeHistory] = useState(false)
  const handlerDiamondHistory = async () => {
    setShowDiamondHistory(true)
  }
  const handlerdiamondConsumeHistory = async () => {
    setShowDiamondConsumeHistory(true)
  }
  const blackUser = async () => {
    const isConfirm = await comfirm.show({
      title: '提醒',
      desc: `是否要封禁用户？`
    });
    if (!isConfirm.confirm) {
      return;
    }
    const res = await request(`/add_user_chat_black?user_id=${user?.user_id}`);
    console.log('res', res)
    if (res?.message === 'ok') {
      Toast.notify({ type: 'success', message: '操作成功' })
      getUserInfo()
    }
  }
  const unblackUser = async () => {
    const isConfirm = await comfirm.show({
      title: '提醒',
      desc: `是否要解除封禁用户？`
    });
    if (!isConfirm.confirm) {
      return;
    }
    const res = await request(`/revoke_user_chat_black?user_id=${user?.user_id}`, {
      method: 'POST'
    });
    console.log('res', res)
    if (res?.message === 'ok') {
      Toast.notify({ type: 'success', message: '操作成功' })
      getUserInfo()
    }
  }
  const blackGroup = async () => {
    const isConfirm = await comfirm.show({
      title: '提醒',
      desc: `是否要封禁群？`
    });
    if (!isConfirm.confirm) {
      return;
    }
    Toast.showLoading('');
    const res = await request(`/ban_user_tg_group?user_id=${user?.user_id}`, {
      method: 'POST'
    });
    Toast.hideLoading();
    console.log('res', res)
    // todo?
    if (res?.error_code === 0) {
      const keys = Object.keys(res?.data);
      const resDesc = keys.reduce((acc, key) => {
        return acc + `群id${key}: ${res?.data[key] ? '成功' : '失败'}\n `;
      }, '');
      dialogAlert.show({
        alertStatus: 2,
        title: '操作结果',
        desc: resDesc
      })
      getUserInfo()
    } else {
      Toast.notify({ type: 'error', message: res.message })
    }
  }
  const unblackGroup = async () => {
    const isConfirm = await comfirm.show({
      title: '提醒',
      desc: `是否要解除封禁群？`
    });
    if (!isConfirm.confirm) {
      return;
    }
    Toast.showLoading('');
    const res = await request(`/unban_user_tg_group?user_id=${user?.user_id}`, {
      method: 'POST'
    });
    Toast.hideLoading();
    console.log('res', res)
    // todo?
    if (res?.error_code === 0) {
      const keys = Object.keys(res?.data);
      const resDesc = keys.reduce((acc, key) => {
        return acc + `群id${key}: ${res?.data[key] ? '成功' : '失败'}\n `;
      }, '');
      dialogAlert.show({
        alertStatus: 2,
        title: '操作结果',
        desc: resDesc
      })
      getUserInfo()
    } else {
      Toast.notify({ type: 'error', message: res.message })
    }
  }
  const unpublishRolePrivilege = async () => {
    const isConfirm = await comfirm.show({
      title: '提醒',
      desc: `是否要解除提审？`
    });
    if (!isConfirm.confirm) {
      return;
    }
    Toast.showLoading('');
    const res = await request(`/remove_publish_role_black?user_id=${user?.user_id}`, {
      method: 'POST'
    });
    Toast.hideLoading();
    if (res?.error_code === 0) {
      dialogAlert.show({
        alertStatus: 2,
        title: '操作结果',
        desc: '解封提审成功'
      })
      getUserInfo()
    } else {
      Toast.notify({ type: 'error', message: res.message })
    }
  }
  const deleteAccount = async () => {
    const isConfirm = await comfirm.show({
      title: '提醒',
      desc: `是否要删除账号？`
    });
    if (!isConfirm.confirm) {
      return;
    }
    //再次确认
    const isConfirm2 = await comfirm.show({
      title: '再次确认',
      desc: `删除账号将会彻底删除用户信息，此过程不可逆，请谨慎操作！`
    });
    if (!isConfirm2.confirm) {
      return;
    }
    Toast.showLoading('');
    const res = await request(`/admin/user/delete_account?user_id=${user?.user_id}`, {
      method: 'POST'
    });
    Toast.hideLoading();
    if (res?.error_code === 0) {
      dialogAlert.show({
        alertStatus: 2,
        title: '操作结果',
        desc: '删除账号成功'
      })
      getUserInfo()
    } else {
      Toast.notify({ type: 'error', message: res.message })
    }
  }

  const [showDecreaseDiamond, setShowDecreaseDiamond] = useState(false)
  const decreaseDiomond = async () => {
    setShowDecreaseDiamond(true)
  }
  const [showAddDiamond, setShowAddDiamond] = useState(false)
  const addDiomond = async () => {
    setShowAddDiamond(true)
  }
  const reflesh = async () => {
    getUserInfo()
  }
  // 检查是否含有true
  const hasBannedStatus = (status: Record<string, boolean> | undefined): boolean => {
    if (!status) return false;
    return Object.values(status).some(value => value === true);
  }
  const reconciliation = async () => {
    const res = await request(`/usdt/reconciliation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        order_id: orderIdRef?.current?.value,
        tx_id: txIdRef?.current?.value,
        chain: chainRef?.current?.value
      })
    });
    console.log('res', res)
    if (res?.error_code === 0) {
      Toast.notify({ type: 'success', message: '操作成功' })
      getUserInfo()
      getUsdtChargeHistory()
    } else {
      Toast.notify({ type: 'error', message: res.message })
    }
  }
  const [showAuthorProfile, setShowAuthorProfile] = useState(false)
  const updateAuthorProfile = async () => {
    setShowAuthorProfile(true)
  }
  const lf_product_id = process.env.NEXT_PUBLIC_ENV === 'dev'?"clzan8xy70000vot1lpsqddvl":"clz5mb2n70000r6wii7pecr8n"
  const GenerateList = ({ linkKey, index, lists }: any) => {
    return <div key={index} className='mt-5'>
      <h3 className='mt-2 mb-2'>用户信息</h3>
      <table className='dark:bg-gray-900 bg-white text-gray-500 text-sm border-collapse mt-2'>
        <thead className='dark:bg-slate-700 bg-slate-300 dark:text-slate-300'>
          <tr className='text-center text-xs sm:text-sm'>
            <th className='border py-2 px-1 border-slate-600'>用户id</th>
            <th className='border py-2 px-4 border-slate-600'>用户名</th>
            <th className='border py-2 px-4 border-slate-600'>用户TGID</th>
            <th className='border py-2 px-4 border-slate-600'>用户注册时间</th>
            <th className='border py-2 px-4 border-slate-600'>作者身份</th>
            <th className='border py-2 px-4 border-slate-600'>最大上传卡片</th>
            <th className='border py-2 px-3 border-slate-600'>累计充值金额</th>
            <th className='border py-2 px-3 border-slate-600'>剩余钻石</th>
            <th className='border py-2 px-3 border-slate-600'>剩余金币</th>
            <th className='border py-2 px-3 border-slate-600'>累计消耗钻石</th>
            <th className='border py-2 px-3 border-slate-600'>累计消耗金币</th>
            <th className='border py-2 px-3 border-slate-600'>封禁幻梦状态</th>
            <th className='border py-2 px-3 border-slate-600'>封禁群状态</th>
            <th className='border py-2 px-3 border-slate-600'>其他信息</th>
            <th className='border py-2 px-3 border-slate-600'>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr key={index} className='ml-3 my-1 border-1 text-gray-400'>
            <td className='border border-slate-700 py-1 px-1'>{user?.user_id}</td>
            <td className='border border-slate-700 py-1 px-1'>{user?.nickname}</td>
            <td className='border border-slate-700 py-1 px-1'>{user?.tg_id}</td>
            <td className='border border-slate-700 py-1 px-1'>{format(user?.created_at * 1000 || 0, 'YYYY-MM-DD HH:mm:ss')}</td>
            <td className='border border-slate-700 py-1 px-1'>{user?.author ? '是' : '否'}</td>
            <td className='border border-slate-700 py-1 px-1'>{user?.role_count}</td>
            <td className='border border-slate-700 py-1 px-1'>{(user?.total_recharge || 0) / 100000}</td>
            <td className='border border-slate-700 py-1 px-1'>{user?.payed_balance}</td>
            <td className='border border-slate-700 py-1 px-1'>{user?.reward_balance}</td>
            <td className='border border-slate-700 py-1 px-1'>{user?.payed_total_consumed}</td>
            <td className='border border-slate-700 py-1 px-1'>{user?.reward_total_consumed}</td>
            <td className='border border-slate-700 py-1 px-1'>{user?.status === 0 && <span className='text-green-500'>正常</span>}{user?.status === 2 && <span className='text-red-500'>封禁</span>}</td>
            <td className='border border-slate-700 py-1 px-1'>
              {user?.tg_group_banned_status && Object.keys(user?.tg_group_banned_status)?.map((item: any) => {
                return <div key={item} className='mt-2'>群{item}: {user?.tg_group_banned_status[item] ? <span className='text-red-500'>封禁</span> : <span className='text-green-500'>正常</span>}</div>
              })}
            </td>
            <td className='border border-slate-700 py-1 px-1'>
              <button type='button' onClick={() => {
                dialogAlert.show({
                  title: '详情',
                  desc: <table className='w-full border-collapse border border-slate-700'>
                    <thead>
                      <tr className='bg-slate-800'>
                        <th className='border py-2 px-3 border-slate-600 text-left'>标题</th>
                        <th className='border py-2 px-3 border-slate-600 text-left'>内容</th>
                      </tr>
                    </thead>
                    <tbody>
                      {user?.detail_list?.map((item: any, idx: number) => (
                        <tr key={idx} className='text-gray-400'>
                          <td className='border border-slate-700 py-1 px-3'><b>{item?.title}</b></td>
                          <td className='border border-slate-700 py-1 px-3'>{item?.content}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>,
                  alertStatus: 2
                })
              }}>点击展示</button>
            </td>
            <td className='border border-slate-700 py-1 px-1'>
              <div className='flex flex-wrap gap-1'>
                <button type='button' className='p-1 px-3 bg-red-500 text-white rounded text-sm' onClick={decreaseDiomond}>扣钻石+金币</button>
                <button type='button' className='p-1 px-3 bg-green-500 text-white rounded text-sm' onClick={addDiomond}>发金币</button>
                <button type='button' className='p-1 px-3 bg-sky-500 text-white rounded text-sm' onClick={handlerDiamondHistory}>钻石获取</button>
                <button type='button' className='p-1 px-3 bg-sky-500 text-white rounded text-sm' onClick={handlerdiamondConsumeHistory}>消费记录</button>
                <button type='button' className='p-1 px-3 bg-red-500 text-white rounded text-sm disabled:bg-gray-500' onClick={blackGroup} disabled={hasBannedStatus(user?.tg_group_banned_status)}>封禁群</button>
                <button type='button' className='p-1 px-3 bg-green-500 text-white rounded text-sm disabled:bg-gray-500' onClick={unblackGroup} disabled={!hasBannedStatus(user?.tg_group_banned_status)}>解封群</button>
                <button type='button' className='p-1 px-3 bg-red-500 text-white rounded text-sm disabled:bg-gray-500' onClick={blackUser} disabled={user?.status === 2}>封禁幻梦</button>
                <button type='button' className='p-1 px-3 bg-green-500 text-white rounded text-sm disabled:bg-gray-500' onClick={unblackUser} disabled={user?.status === 0}>解封幻梦</button>
                <button type='button' className='p-1 px-3 bg-green-500 text-white rounded text-sm disabled:bg-gray-500' onClick={unpublishRolePrivilege} disabled={user?.publish_role_privilege !== false}>解封提审</button>
                <button type='button' className='p-1 px-3 bg-red-500 text-white rounded text-sm disabled:bg-gray-500' onClick={deleteAccount}>删除账号</button>
                <button type='button' className='p-1 px-3 bg-red-500 text-white rounded text-sm disabled:bg-gray-500' onClick={getCurrentUserUsdtChargeHistory}>查询用户USDT充值历史</button>
                <button onClick={updateAuthorProfile} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>作者身份和卡片数量设置</button>
                <Link href={`https://aidream6688.grafana.net/d/cdoxxygbzqygwf/302ea2e?var-user_id=${user?.user_id}`} className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' target='_blank'>用户报表</Link>
                <Link href={`http://langfuse-v3.198432.xyz:3300/project/${lf_product_id}/traces?search=${user?.user_id}`} className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' target='_blank'>LangFuse</Link>
                <Link href={`http://grafana.198432.xyz:3000/explore?schemaVersion=1&panes=%7B%22xfy%22:%7B%22datasource%22:%22fe5w97a5xp2wwb%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bjob%3D%5C%22pm2-log%5C%22%7D%20%7C%3D%20%60${user?.user_id}%60%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22fe5w97a5xp2wwb%22%7D,%22editorMode%22:%22builder%22%7D%5D,%22range%22:%7B%22from%22:%22now-24h%22,%22to%22:%22now%22%7D%7D%7D&orgId=1`} className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' target='_blank'>用户日志</Link>
                <Link href={`https://aidream6688.grafana.net/d/tatvvmd/20eff49?orgId=1&from=now-6h&to=now&timezone=browser&var-user_id=${user?.user_id}`} className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3' target='_blank'>用户聊天</Link>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  }

  return (
    <>
      {<main className="main-height bg-black w-full con-width px-3 pt-6">
        <div className='mb-5'>
          <div className="">
            <h2 className='text-xl mt-5 inline-block mr-1'>用户ID&TgId: </h2>
            <input
              type="text" ref={userRef}
              id="user"
              autoComplete="user"
              required
              placeholder='请输入用户ID或者TgId'
              className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
            />
            <button onClick={getUserInfo} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>通过用户id查询</button>
            <button onClick={getUserInfoByTgId} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>通过TgId查询</button>
            <button onClick={getUserUsdtChargeHistory} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>查询用户USDT充值历史</button>
            <button onClick={getUsdtChargeHistory} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>全量查询USDT充值历史</button>
          </div>
          <div className="space-x-2">
            <h2 className='text-xl mt-5 inline-block mr-1'>USDT补单: </h2>
            <input
              type="text" ref={orderIdRef}
              id="orderId"
              autoComplete="orderId"
              required
              placeholder='请输入order_id'
              className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
            />
            <input
              type="text" ref={txIdRef}
              id="txId"
              autoComplete="txId"
              required
              placeholder='请输入tx_id'
              className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
            />
            <div className='inline-block'>
              选择链：
              <select ref={chainRef} className='bg-gray-800 px-2 py-1 rounded-md'>
                {chains.map((chain: any) => (
                  <option key={chain.value} value={chain.value}>{chain.name}({chain.value})</option>
                ))}
              </select>
            </div>
            <button onClick={reconciliation} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>补单</button>
          </div>

          <div className="">
            <h2 className='text-xl mt-5 inline-block mr-1'>订单Id: </h2>
            <input
              type="text" ref={order1IdRef}
              id="order"
              autoComplete="order"
              required
              placeholder='请输入内部订单ID或外部订单Id'
              className="w-60 rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
            />
            <button onClick={getOrderById} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>通过内部订单IDid查询</button>
            <button onClick={getOrderByOuterId} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>通过外部订单Id查询</button>
          </div>


          <div className="">
            <h2 className='text-xl mt-5 inline-block mr-1'>查询用户参与消耗钻石活动的完成进度: </h2>
            <input
              type="text" ref={user1Ref}
              id="user1"
              autoComplete="user1"
              required
              placeholder='请输入用户ID或者TgId'
              className="rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-500 focus:ring-2 focus:ring-inset focus:ring-gray-600 sm:text-sm sm:leading-6 bg-gray-800 px-3"
            />
            <button onClick={() => { getUserActivityInfo(true) }} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>通过用户id查询</button>
            <button onClick={() => { getUserActivityInfo(false) }} type='button' className='p-2 px-5 bg-purple-500 text-white rounded text-sm ml-3'>通过TgId查询</button>
          </div>

          {user && <GenerateList linkKey={userRef?.current?.value} index={0} />}
          {orderInfo && <OrderInfo orderInfo={orderInfo} />}
          {userUsdtChargeHistory && <UserUsdtChargeHistory userUsdtChargeHistory={userUsdtChargeHistory} />}
          {usdtChargeHistory && <UsdtChargeHistory usdtChargeHistory={usdtChargeHistory} />}
          {userActivityInfo && <ActivityInfo info={userActivityInfo} />}
        </div>

        {showDiamondHistory && <DiamonHistory uid={user?.user_id} onClose={() => { setShowDiamondHistory(false) }} />}
        {showDiamondConsumeHistory && <DiamonConsumeHistory uid={user?.user_id} onClose={() => { setShowDiamondConsumeHistory(false) }} />}
        {showDecreaseDiamond && <DecreaseDiamon uid={user?.user_id} balance={user?.balance} onClose={() => { setShowDecreaseDiamond(false) }} reflesh={reflesh} />}
        {showAddDiamond && <AddDiamon uid={user?.user_id} payed_balance={user?.payed_balance} reward_balance={user?.reward_balance} onClose={() => { setShowAddDiamond(false) }} reflesh={reflesh} />}
        {showAuthorProfile && <UpdateUserCard uid={user?.user_id} cards={user?.role_count} author={user?.author} onClose={() => { setShowAuthorProfile(false) }} reflesh={reflesh} />}
      </main>}
    </>
  )
}

export default React.memo(Pay)
